import * as Sparkplug from 'sparkplug-payload';

const sparkplugHandler = Sparkplug.get('spBv1.0');

export interface OutgoingRecord {
  payload: any;
  topic: string;
}

export class SparkplugKinesisRecord {
  readonly rawSparkplugMessage: string;

  readonly clientId: string;

  readonly arrivalTime: number;

  readonly topic: string;

  readonly sparkplugMessage: any;

  readonly traceId: string;

  private readonly topicParts: string[];

  get integration(): string {
    return this.topicParts[1];
  }

  get messageType(): string {
    return this.topicParts[2];
  }

  get organizationId(): string {
    return this.topicParts[3];
  }

  get deviceId(): string {
    return this.topicParts[4];
  }

  constructor(rawMessage: string) {
    const payload = Buffer.from(rawMessage, 'base64').toString('utf8');
    const record = JSON.parse(payload);

    this.rawSparkplugMessage = record.sparkplug;
    this.clientId = record.clientId;
    this.arrivalTime = record.arrivalTime;
    this.topicParts = record.topic.split('/');
    this.topic = record.topic;
    this.traceId = record.traceId;

    this.sparkplugMessage = sparkplugHandler.decodePayload(Buffer.from(this.rawSparkplugMessage, 'base64'));
  }
}
