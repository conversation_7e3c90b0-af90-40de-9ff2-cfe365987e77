import { Duration, aws_lambda as Lambda, aws_kinesis as <PERSON>nesis, Fn } from 'aws-cdk-lib';
import { RetentionDays } from 'aws-cdk-lib/aws-logs';

import { ConfigManifest } from '@carrier/backend-lib-infrastructure';
import { Runtime } from 'aws-cdk-lib/aws-lambda';
import { ConfigVariables } from './ConfigVariables';

const formatName = (name: string) => name.replace(/[^a-zA-Z0-9]/g, '');

export class Config {
  static readonly adapterName = formatName(ConfigVariables.adapterName);

  static readonly integrationName = formatName(ConfigVariables.integrationName);

  static readonly sourceId = `integration-adapter-${this.adapterName}`;

  static isPersonalEnvironment = false;

  static from(manifest: ConfigManifest, partition: string, stage: string, region: string, account: string) {
   
    const config = ConfigVariables.from(manifest, partition, region, account, stage);
    const dlqAPI = Fn.importValue(config.dlq.apiDomainExportName);
    const dlqEndpoint = `${dlqAPI}/graphql`;
    const runtime =  Runtime.NODEJS_20_X;

    this.isPersonalEnvironment = !ConfigVariables.primaryStages.includes(stage);

    return {
      kinesis: {
        adapterStream: {
          streamMode: Kinesis.StreamMode.ON_DEMAND,
          retentionPeriod: Duration.days(7),
          streamName: ConfigManifest.applyNameSpace(`carrier-io-${this.adapterName}-integration-adapter-stream`, stage),
          roleName: ConfigManifest.applyNameSpace(`carrier-io-${this.adapterName}-integration-adapter-role`, stage),
        },
        targetStreams: config.targetStreamArns,
      },
      iotCoreRules: config.iotRules.map((rule) => {
        const stageRuleName = stage.replace('-', '_');
        return {
          ruleName: `carrier_io_${this.adapterName}_integration_adapter_${rule.name}_${stageRuleName}`,
          kinesisPartitionKey: rule.kinesisPartitionKey,
          awsIotSqlVersion: '2016-03-23-beta',
          sql: rule.iotRule,
        };
      }),
      s3Bucket: {
        bucketName: ConfigManifest.applyNameSpace(`${config.s3.bucketName}`, stage),
      },
      lambdaFunction: {
        properties: {
          entry: `${__dirname}/../../src/lambda/IntegrationAdapterLambda.ts`,
          handler: 'index.IntegrationAdapterLambda.handler',
          runtime,
          functionName: ConfigManifest.applyNameSpace(
            `carrier-io-${this.adapterName}-integration-adapter-lambda`,
            stage,
          ),
          memorySize: 1024,
          timeout: Duration.minutes(5),
          environment: {
            TARGET_STREAM_NAME_DATA: config.targetStreamArns.data.split('/').pop() || '',
            TARGET_STREAM_NAME_LIFECYCLE: config.targetStreamArns.lifecycle.split('/').pop() || '',
            TARGET_AWS_REGION: region || '',
            IS_VALIDATION_TURNED_OFF: config.pipelineSdk.isValidationTurnedOff,
            IS_BRICK_CLASS_VALUE_VALIDATION_OFF: config.pipelineSdk.isBrickClassValueValidationOff,
            ENABLE_BUFFERING_DEBUG_LOGS: this.isPersonalEnvironment ? 'false' : 'true',
            DLQ_SOURCE_ID: this.sourceId,
            DLQ_PLATFORM_ID: this.integrationName,
            DLQ_API_URL: dlqEndpoint,
            DLQ_NUM_OF_ATTEMPTS: config.dlq.numOfAttempts,
            LOG_LEVEL: config.logLevel,
            OTEL_BACKEND_EXPORTERS: JSON.stringify(config.otelBackendExporters),
            OTEL_LOG_LEVEL: JSON.stringify(config.otelLogLevel),
            OTEL_SERVICE_NAME: config.otelServiceName,
            OTEL_LOGS_SAMPLING_RATE: config.otelLogsSamplingRate,
            AWS_LAMBDA_EXEC_WRAPPER: config.lambdaExecWrapper,
          },
          bundling: {
            nodeModules: ['sparkplug-payload'],
            minify: true,
            sourceMap: true,
          },
        },
        eventSource: {
          batchSize: 100,
          retryAttempts: 3,
          startingPosition: Lambda.StartingPosition.TRIM_HORIZON,
        },
        isEnhancedFanout: config.isEnhancedFanout,
        logs: {
          name: `carrier-io-${this.adapterName}-integration-adapter-lambda-${stage}`,
          logGroupName: `/aws/lambda/carrier-io-${this.adapterName}-integration-adapter-lambda-${stage}`,
          logGroupId: `carrier-io-${this.adapterName}-integration-adapter-lambda-${stage}_lg`,
          logRetentionId: `carrier-io-${this.adapterName}-integration-adapter-lambda-${stage}_lr`,
          retention: RetentionDays.ONE_MONTH,
        },
      },
      dlqAPIGwAppId: config.dlq.apiIdExportName,
      dlqAPIDomain: config.dlq.apiDomainExportName,
      otelLogServiceAccountRegion: config.otelLogServiceAccountRegion,
      otelLogServiceAccountLambdaVersion: config.otelLogServiceAccountLambdaVersion,
      otelLogServiceAccount: config.otelLogServiceAccount,
    };
  }
}
