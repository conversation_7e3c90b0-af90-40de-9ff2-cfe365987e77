import { PointMetric } from "./types";

const parseSeriesValue = (value: any): string | undefined => {
  if (!value) {
    return undefined;
  }
  const strValue = String(value);
  if (/^\d+$/.test(strValue)) {
    return strValue; // Return number as string
  }
  const result = strValue.replace(/\d+/g, '').trim();
  return result || undefined;
};


const findMetricByName = (metrics: PointMetric[], possibleNames: string[]) => {
  return metrics.find((metric) => possibleNames.includes(metric.name));
};



export const handleBrickClasses = (
  metrics: PointMetric[],
  topic: string
): string | undefined => {

  // EquipmentFamily and picController verison we are getting in DBIRTH and DDATA
  if (["DBIRTH", "DDATA"].includes(topic)) {
    metrics.forEach((currentMetric: any) => {
      switch (currentMetric.name) {
        case "equipmentFamily":
          currentMetric.name = "equipmentFamily";
          currentMetric.properties.brickClass.value = "Equipment_Family";
          break;
        case "softwareVersion":
          currentMetric.name = "softwareVersion";
          currentMetric.properties.brickClass.value = "Software_Version";
          break;
        case "picControllerVersion":
          currentMetric.name = "picControllerVersion";
          currentMetric.properties.brickClass.value = "PIC_Controller_Version";
          break;
        case "serialNumber":
          currentMetric.name = "serialNumber";
          currentMetric.properties.brickClass.value = "Serial_Number";
          break;
        default:
      }
    });
  }
  // Check if equipmentFamily exists in metrics
  const equipmentFamilyMetric = findMetricByName(metrics, [
    "equipmentFamily",
    "Chiller/equipmentFamily",
  ]);

  const picControllerVersionMetric = findMetricByName(metrics, [
    "picControllerVersion",
    "Chiller/picControllerVersion",
  ]);

  if (equipmentFamilyMetric?.value && picControllerVersionMetric?.value) {
    const seriesNumber = parseSeriesValue(equipmentFamilyMetric.value);
    if (seriesNumber) {
      return `${seriesNumber}_${picControllerVersionMetric.value}series`;
    }
  }

  return undefined;
};
