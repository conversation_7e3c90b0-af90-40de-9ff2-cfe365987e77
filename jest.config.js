const tsconfig = require('./tsconfig.json');

module.exports = {
  collectCoverage: true,
  collectCoverageFrom: ['src/**/*.ts'],
  coverageDirectory: './coverage',
  coveragePathIgnorePatterns: [],
  coverageReporters: ['lcov', 'text'],
  testEnvironment: 'node',
  roots: ['test/src'],
  testMatch: ['**/+(*.)+(spec|test).+(ts|js)?(x)'],
  transform: {
    '^.+\\.tsx?$': 'ts-jest'
  },
  moduleFileExtensions: ['ts', 'tsx', 'js', 'jsx'],
  setupFiles: ['./test/jest.setupEnv.ts'],
  moduleNameMapper: {
    'models/SparkplugKinesisRecord': '<rootDir>/src/models/SparkplugKinesisRecord',
    'models/LwtRecord': '<rootDir>/src/models/LwtRecord'
  },
  testResultsProcessor: "jest-sonar-reporter"
};