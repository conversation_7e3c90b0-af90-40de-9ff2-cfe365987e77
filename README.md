# CJC Integration Adapter

## Contents

[TOC]

## Overview

The CJC integration adapter (IA) is the IA for the CJC(Carrier Japan Corporation) HVAC platform built on carrier.io. It is used to transform the data coming from the cjc Toshiba chillers (MC and UC ) through the Gateway devices. The integration adapter receives metrics associated with the chillers. 
The CJC IA uses [Publisher Library SDK](https://bitbucket.org/carrier-digital/carrier-io-backend-lib-pipeline-sdk/src/main/) for publishing messages to carrier.io main pipelines. Invalid messages will be sent to the Dead Letter Queue service for future reprocessing.

The following infrastructure is included in this module:

1. `IOT Core Rules` - One or more IOT Core rules to route data from IOT core to the kinesis stream.
2. `Kinesis Stream` - A Kinesis stream for streaming data off of IOT Core that mathces your IOT Core Rule.
3. `*Fan-Out Stream Consumer Lambda` - **(optional)** Deploys if you enable enhanced fan out for the Kinesis stream.
4. `Lambda Function` - Your code to process incoming Kinesis records.
5. `IAM Permissions` - Permissions configured for your lambda to write to the DLQ and to the destination Kinesis streams (if setup).
6. `Supporting Components` - Other components such as IAM rules and polices etc for everything to work.

### Repository Documentation 

| Markdown Link                                                                                                                        | Description |
|--------------------------------------------------------------------------------------------------------------------------------------| ----------- |
| [Contributing Guide](https://bitbucket.org/carrier-digital/ahp-integrationadapter-cjc/src/main/CONTRIBUTING.md) | Contains the documentation for developers making changes in the repository.|
| [Docs ReadMe](https://bitbucket.org/carrier-digital/ahp-integrationadapter-cjc/src/main/README.md)                                                                                                        | Contains TypeDoc Generated Markdown for Classes, Enums, etc|

### Relevant Confluence Documentation 

| Confluence Page                                                                                                          | Description                                                                               |
| ------------------------------------------------------------------------------------------------------------------------ | ----------------------------------------------------------------------------------------- |
| [Integration Adapters](https://carrier-digital.atlassian.net/wiki/spaces/IO/pages/2167505214/) | Overview of carrier.io Integration Adapters 
| [CJC Integration Adapter Data Flow](https://carrier-digital.atlassian.net/wiki/spaces/SMART/pages/3260154232/CJC+Integration+Adapter/) | Overview of cjc integration Adapter                                              |
| [Publisher Library SDK](https://carrier-digital.atlassian.net/wiki/spaces/IO/pages/2458976264/) | Overview for Publisher Library SDK         |
| [DLQ Service](https://carrier-digital.atlassian.net/wiki/spaces/IO/pages/2368274588)          		  |  Overview for the Dead Letter Queue Service            			 |
| [Carrier.io Glossary](https://carrier-digital.atlassian.net/wiki/spaces/IO/pages/2108063771/)                    | Glossary of Carrier.io Terms and Definitions                                  |


## Architecture 

To view the architecture and general information for the Data Pipeline Integration Adapters, visit [DP Integration Adapters](https://carrier-digital.atlassian.net/wiki/spaces/IO/pages/2167505214/).

## Examples 

Sample CJC Data:


      Topic: spBv1.0/cjc/DDATA/BMSIFRM1280U40789997/RUAZLIH5H5224950315  

        
```
{
    "timestamp": 1725454461168,
    "sparkplugMessage": {
      "topic": "spBv1.0/ahp/DDATA/BMSIFRM1280U40789997/RUAZLIH5H5224950315",
      "payload": {
        "timestamp": 1725454456000,
        "metrics": [
          {
            "name": "Chiller/ALARM",
            "type": "Int8",
            "value": 2,
            "properties": {
              "brickClass": {
                "value": "Fault_Status",
                "type": "String"
              },
              "cioTags": {
                "value": "zephyr_tag_1ad37797-53a4-4af9-a6b4-8b4cf9614e0e",
                "type": "String"
              }
            }
          },
          {
            "name": "Chiller/ALMCD",
            "type": "Int64",
            "value": {
              "low": 3,
              "high": 0,
              "unsigned": true
            },
            "properties": {
              "brickClass": {
                "value": "Alarm_Code_Status",
                "type": "String"
              },
              "cioTags": {
                "value": "zephyr_tag_1ad37797-53a4-4af9-a6b4-8b4cf9614e0e",
                "type": "String"
              }
            }
          },
          {
            "name": "Chiller/ALTCD",
            "type": "Int64",
            "value": {
              "low": 4,
              "high": 0,
              "unsigned": true
            },
            "properties": {
              "brickClass": {
                "value": "Alert_Code_Status",
                "type": "String"
              },
              "cioTags": {
                "value": "zephyr_tag_1ad37797-53a4-4af9-a6b4-8b4cf9614e0e",
                "type": "String"
              }
            }
          },
          {
            "name": "Chiller/OAR",
            "type": "Float",
            "value": 20,
            "properties": {
              "brickClass": {
                "value": "Outside_Air_Humidity_Sensor",
                "type": "String"
              },
              "cioTags": {
                "value": "zephyr_tag_1ad37797-53a4-4af9-a6b4-8b4cf9614e0e",
                "type": "String"
              }
            }
          },
          {
            "name": "Chiller/OAT",
            "type": "Float",
            "value": 12,
            "properties": {
              "brickClass": {
                "value": "Outside_Air_Temperature_Sensor",
                "type": "String"
              },
              "cioTags": {
                "value": "zephyr_tag_1ad37797-53a4-4af9-a6b4-8b4cf9614e0e",
                "type": "String"
              }
            }
          },
          {
            "name": "Chiller/REMOTE",
            "type": "Int16",
            "value": 1,
            "properties": {
              "brickClass": {
                "value": "Operation_Mode_Status",
                "type": "String"
              },
              "cioTags": {
                "value": "zephyr_tag_1ad37797-53a4-4af9-a6b4-8b4cf9614e0e",
                "type": "String"
              }
            }
          },
          {
            "name": "Chiller/RUNS",
            "type": "Int8",
            "value": 1,
            "properties": {
              "brickClass": {
                "value": "Run_Status",
                "type": "String"
              },
              "cioTags": {
                "value": "zephyr_tag_1ad37797-53a4-4af9-a6b4-8b4cf9614e0e",
                "type": "String"
              }
            }
          },
          {
            "name": "Chiller/UNITNO",
            "type": "UInt32",
            "value": {
              "low": 0,
              "high": 0,
              "unsigned": true
            },
            "properties": {
              "brickClass": {
                "value": "Unit_Number_Status",
                "type": "String"
              },
              "cioTags": {
                "value": "zephyr_tag_1ad37797-53a4-4af9-a6b4-8b4cf9614e0e",
                "type": "String"
              }
            }
          },
          {
            "name": "LineA/CAPA",
            "type": "Float",
            "value": 6,
            "properties": {
              "brickClass": {
                "value": "Capacity_Sensor",
                "type": "String"
              },
              "cioTags": {
                "value": "zephyr_tag_1ad37797-53a4-4af9-a6b4-8b4cf9614e0e",
                "type": "String"
              }
            }
          },
          {
            "name": "LineB/CAPB",
            "type": "Float",
            "value": 7,
            "properties": {
              "brickClass": {
                "value": "Capacity_Sensor",
                "type": "String"
              },
              "cioTags": {
                "value": "zephyr_tag_1ad37797-53a4-4af9-a6b4-8b4cf9614e0e",
                "type": "String"
              }
            }
          },
          {
            "name": "LineA/DNDTYA",
            "type": "UInt8",
            "value": 23,
            "properties": {
              "brickClass": {
                "value": "Demand_Target_Mode_Status",
                "type": "String"
              },
              "cioTags": {
                "value": "zephyr_tag_1ad37797-53a4-4af9-a6b4-8b4cf9614e0e",
                "type": "String"
              }
            }
          },
          {
            "name": "LineB/DNDTYB",
            "type": "UInt8",
            "value": 33,
            "properties": {
              "brickClass": {
                "value": "Demand_Target_Mode_Status",
                "type": "String"
              },
              "cioTags": {
                "value": "zephyr_tag_1ad37797-53a4-4af9-a6b4-8b4cf9614e0e",
                "type": "String"
              }
            }
          },
          {
            "name": "LineA/EWTA",
            "type": "Float",
            "value": 2,
            "properties": {
              "brickClass": {
                "value": "Entering_Water_Temperature_Sensor",
                "type": "String"
              },
              "cioTags": {
                "value": "zephyr_tag_1ad37797-53a4-4af9-a6b4-8b4cf9614e0e",
                "type": "String"
              }
            }
          },
          {
            "name": "LineB/EWTB",
            "type": "Float",
            "value": 3,
            "properties": {
              "brickClass": {
                "value": "Entering_Water_Temperature_Sensor",
                "type": "String"
              },
              "cioTags": {
                "value": "zephyr_tag_1ad37797-53a4-4af9-a6b4-8b4cf9614e0e",
                "type": "String"
              }
            }
          },
          {
            "name": "LineA/FDNDA",
            "type": "Int8",
            "value": 2,
            "properties": {
              "brickClass": {
                "value": "Demand_Switch_Status",
                "type": "String"
              },
              "cioTags": {
                "value": "zephyr_tag_1ad37797-53a4-4af9-a6b4-8b4cf9614e0e",
                "type": "String"
              }
            }
          },
          {
            "name": "LineB/FDNDB",
            "type": "Int8",
            "value": 3,
            "properties": {
              "brickClass": {
                "value": "Demand_Switch_Status",
                "type": "String"
              },
              "cioTags": {
                "value": "zephyr_tag_1ad37797-53a4-4af9-a6b4-8b4cf9614e0e",
                "type": "String"
              }
            }
          },
          {
            "name": "LineA/FLOWA",
            "type": "Float",
            "value": 2,
            "properties": {
              "brickClass": {
                "value": "Heat_Pump_Side_Water_Flow_Sensor",
                "type": "String"
              },
              "cioTags": {
                "value": "zephyr_tag_1ad37797-53a4-4af9-a6b4-8b4cf9614e0e",
                "type": "String"
              }
            }
          },
          {
            "name": "LineB/FLOWB",
            "type": "Float",
            "value": 3,
            "properties": {
              "brickClass": {
                "value": "Heat_Pump_Side_Water_Flow_Sensor",
                "type": "String"
              },
              "cioTags": {
                "value": "zephyr_tag_1ad37797-53a4-4af9-a6b4-8b4cf9614e0e",
                "type": "String"
              }
            }
          },
          {
            "name": "LineA/FTSHFA",
            "type": "Int8",
            "value": 0,
            "properties": {
              "brickClass": {
                "value": "Set_Temperature_Shift_Switch_Status",
                "type": "String"
              },
              "cioTags": {
                "value": "zephyr_tag_1ad37797-53a4-4af9-a6b4-8b4cf9614e0e",
                "type": "String"
              }
            }
          },
          {
            "name": "LineB/FTSHFB",
            "type": "Int8",
            "value": 0,
            "properties": {
              "brickClass": {
                "value": "Set_Temperature_Shift_Switch_Status",
                "type": "String"
              },
              "cioTags": {
                "value": "zephyr_tag_1ad37797-53a4-4af9-a6b4-8b4cf9614e0e",
                "type": "String"
              }
            }
          },
          {
            "name": "LineA/FWCTRA",
            "type": "UInt8",
            "value": 0,
            "properties": {
              "brickClass": {
                "value": "Flow_Rate_Mode_Status",
                "type": "String"
              },
              "cioTags": {
                "value": "zephyr_tag_1ad37797-53a4-4af9-a6b4-8b4cf9614e0e",
                "type": "String"
              }
            }
          },
          {
            "name": "LineB/FWCTRB",
            "type": "UInt8",
            "value": 0,
            "properties": {
              "brickClass": {
                "value": "Flow_Rate_Mode_Status",
                "type": "String"
              },
              "cioTags": {
                "value": "zephyr_tag_1ad37797-53a4-4af9-a6b4-8b4cf9614e0e",
                "type": "String"
              }
            }
          },
          {
            "name": "LineA/GRNOA",
            "type": "UInt8",
            "value": 0,
            "properties": {
              "brickClass": {
                "value": "Device_Group_Parameter",
                "type": "String"
              },
              "cioTags": {
                "value": "zephyr_tag_1ad37797-53a4-4af9-a6b4-8b4cf9614e0e",
                "type": "String"
              }
            }
          },
          {
            "name": "LineB/GRNOB",
            "type": "UInt8",
            "value": 0,
            "properties": {
              "brickClass": {
                "value": "Device_Group_Parameter",
                "type": "String"
              },
              "cioTags": {
                "value": "zephyr_tag_1ad37797-53a4-4af9-a6b4-8b4cf9614e0e",
                "type": "String"
              }
            }
          },
          {
            "name": "LineA/HPA",
            "type": "Float",
            "value": 0,
            "properties": {
              "brickClass": {
                "value": "Differential_Pressure_Sensor",
                "type": "String"
              },
              "cioTags": {
                "value": "zephyr_tag_1ad37797-53a4-4af9-a6b4-8b4cf9614e0e",
                "type": "String"
              }
            }
          },
          {
            "name": "LineB/HPB",
            "type": "Float",
            "value": 0,
            "properties": {
              "brickClass": {
                "value": "Differential_Pressure_Sensor",
                "type": "String"
              },
              "cioTags": {
                "value": "zephyr_tag_1ad37797-53a4-4af9-a6b4-8b4cf9614e0e",
                "type": "String"
              }
            }
          },
          {
            "name": "LineA/HSEPA",
            "type": "Float",
            "value": 0,
            "properties": {
              "brickClass": {
                "value": "Heat_Pump_Electric_Power_Sensor",
                "type": "String"
              },
              "cioTags": {
                "value": "zephyr_tag_1ad37797-53a4-4af9-a6b4-8b4cf9614e0e",
                "type": "String"
              }
            }
          },
          {
            "name": "LineB/HSEPB",
            "type": "Float",
            "value": 0,
            "properties": {
              "brickClass": {
                "value": "Heat_Pump_Electric_Power_Sensor",
                "type": "String"
              },
              "cioTags": {
                "value": "zephyr_tag_1ad37797-53a4-4af9-a6b4-8b4cf9614e0e",
                "type": "String"
              }
            }
          },
          {
            "name": "LineA/IEPA",
            "type": "UInt32",
            "value": {
              "low": 0,
              "high": 0,
              "unsigned": true
            },
            "properties": {
              "brickClass": {
                "value": "Heat_Pump_Electrical_Energy_Usage_Sensor",
                "type": "String"
              },
              "cioTags": {
                "value": "zephyr_tag_1ad37797-53a4-4af9-a6b4-8b4cf9614e0e",
                "type": "String"
              }
            }
          },
          {
            "name": "LineB/IEPB",
            "type": "UInt32",
            "value": {
              "low": 0,
              "high": 0,
              "unsigned": true
            },
            "properties": {
              "brickClass": {
                "value": "Heat_Pump_Electrical_Energy_Usage_Sensor",
                "type": "String"
              },
              "cioTags": {
                "value": "zephyr_tag_1ad37797-53a4-4af9-a6b4-8b4cf9614e0e",
                "type": "String"
              }
            }
          },
          {
            "name": "LineA/LWTA",
            "type": "Float",
            "value": 0,
            "properties": {
              "brickClass": {
                "value": "Leaving_Water_Temperature_Sensor",
                "type": "String"
              },
              "cioTags": {
                "value": "zephyr_tag_1ad37797-53a4-4af9-a6b4-8b4cf9614e0e",
                "type": "String"
              }
            }
          },
          {
            "name": "LineB/LWTB",
            "type": "Float",
            "value": 0,
            "properties": {
              "brickClass": {
                "value": "Leaving_Water_Temperature_Sensor",
                "type": "String"
              },
              "cioTags": {
                "value": "zephyr_tag_1ad37797-53a4-4af9-a6b4-8b4cf9614e0e",
                "type": "String"
              }
            }
          },
          {
            "name": "LineA/MODEA",
            "type": "Int8",
            "value": 0,
            "properties": {
              "brickClass": {
                "value": "Run_Mode_Status",
                "type": "String"
              },
              "cioTags": {
                "value": "zephyr_tag_1ad37797-53a4-4af9-a6b4-8b4cf9614e0e",
                "type": "String"
              }
            }
          },
          {
            "name": "LineB/MODEB",
            "type": "Int8",
            "value": 0,
            "properties": {
              "brickClass": {
                "value": "Run_Mode_Status",
                "type": "String"
              },
              "cioTags": {
                "value": "zephyr_tag_1ad37797-53a4-4af9-a6b4-8b4cf9614e0e",
                "type": "String"
              }
            }
          },
          {
            "name": "LineA/MVA",
            "type": "Float",
            "value": 0,
            "properties": {
              "brickClass": {
                "value": "Bypass_Valve_Position_Sensor",
                "type": "String"
              },
              "cioTags": {
                "value": "zephyr_tag_1ad37797-53a4-4af9-a6b4-8b4cf9614e0e",
                "type": "String"
              }
            }
          },
          {
            "name": "LineB/MVB",
            "type": "Float",
            "value": 0,
            "properties": {
              "brickClass": {
                "value": "Bypass_Valve_Position_Sensor",
                "type": "String"
              },
              "cioTags": {
                "value": "zephyr_tag_1ad37797-53a4-4af9-a6b4-8b4cf9614e0e",
                "type": "String"
              }
            }
          },
          {
            "name": "LineA/PPEPA",
            "type": "Float",
            "value": 0,
            "properties": {
              "brickClass": {
                "value": "Pump_Electric_Power_Sensor",
                "type": "String"
              },
              "cioTags": {
                "value": "zephyr_tag_1ad37797-53a4-4af9-a6b4-8b4cf9614e0e",
                "type": "String"
              }
            }
          },
          {
            "name": "LineB/PPEPB",
            "type": "Float",
            "value": 0,
            "properties": {
              "brickClass": {
                "value": "Pump_Electric_Power_Sensor",
                "type": "String"
              },
              "cioTags": {
                "value": "zephyr_tag_1ad37797-53a4-4af9-a6b4-8b4cf9614e0e",
                "type": "String"
              }
            }
          },
          {
            "name": "LineA/RUNA",
            "type": "Int8",
            "value": 0,
            "properties": {
              "brickClass": {
                "value": "Run_Status",
                "type": "String"
              },
              "cioTags": {
                "value": "zephyr_tag_1ad37797-53a4-4af9-a6b4-8b4cf9614e0e",
                "type": "String"
              }
            }
          },
          {
            "name": "LineB/RUNB",
            "type": "Int8",
            "value": 0,
            "properties": {
              "brickClass": {
                "value": "Run_Status",
                "type": "String"
              },
              "cioTags": {
                "value": "zephyr_tag_1ad37797-53a4-4af9-a6b4-8b4cf9614e0e",
                "type": "String"
              }
            }
          },
          {
            "name": "LineA/SFLWA",
            "type": "Float",
            "value": 0,
            "properties": {
              "brickClass": {
                "value": "Load_Side_Water_Flow_Sensor",
                "type": "String"
              },
              "cioTags": {
                "value": "zephyr_tag_1ad37797-53a4-4af9-a6b4-8b4cf9614e0e",
                "type": "String"
              }
            }
          },
          {
            "name": "LineB/SFLWB",
            "type": "Float",
            "value": 0,
            "properties": {
              "brickClass": {
                "value": "Load_Side_Water_Flow_Sensor",
                "type": "String"
              },
              "cioTags": {
                "value": "zephyr_tag_1ad37797-53a4-4af9-a6b4-8b4cf9614e0e",
                "type": "String"
              }
            }
          },
          {
            "name": "LineA/SPHPA",
            "type": "Float",
            "value": 0,
            "properties": {
              "brickClass": {
                "value": "Differential_Pressure_Setpoint",
                "type": "String"
              },
              "cioTags": {
                "value": "zephyr_tag_1ad37797-53a4-4af9-a6b4-8b4cf9614e0e",
                "type": "String"
              }
            }
          },
          {
            "name": "LineB/SPHPB",
            "type": "Float",
            "value": 0,
            "properties": {
              "brickClass": {
                "value": "Differential_Pressure_Setpoint",
                "type": "String"
              },
              "cioTags": {
                "value": "zephyr_tag_1ad37797-53a4-4af9-a6b4-8b4cf9614e0e",
                "type": "String"
              }
            }
          },
          {
            "name": "LineA/SPTCA",
            "type": "Float",
            "value": 0,
            "properties": {
              "brickClass": {
                "value": "Chilled_Water_Temperature_Setpoint",
                "type": "String"
              },
              "cioTags": {
                "value": "zephyr_tag_1ad37797-53a4-4af9-a6b4-8b4cf9614e0e",
                "type": "String"
              }
            }
          },
          {
            "name": "LineB/SPTCB",
            "type": "Float",
            "value": 0,
            "properties": {
              "brickClass": {
                "value": "Chilled_Water_Temperature_Setpoint",
                "type": "String"
              },
              "cioTags": {
                "value": "zephyr_tag_1ad37797-53a4-4af9-a6b4-8b4cf9614e0e",
                "type": "String"
              }
            }
          },
          {
            "name": "LineA/SPTHA",
            "type": "Float",
            "value": 0,
            "properties": {
              "brickClass": {
                "value": "Hot_Water_Temperature_Setpoint",
                "type": "String"
              },
              "cioTags": {
                "value": "zephyr_tag_1ad37797-53a4-4af9-a6b4-8b4cf9614e0e",
                "type": "String"
              }
            }
          },
          {
            "name": "LineB/SPTHB",
            "type": "Float",
            "value": 0,
            "properties": {
              "brickClass": {
                "value": "Hot_Water_Temperature_Setpoint",
                "type": "String"
              },
              "cioTags": {
                "value": "zephyr_tag_1ad37797-53a4-4af9-a6b4-8b4cf9614e0e",
                "type": "String"
              }
            }
          },
          {
            "name": "LineA/STAGEA",
            "type": "Float",
            "value": 0,
            "properties": {
              "brickClass": {
                "value": "Percent_Load_Parameter",
                "type": "String"
              },
              "cioTags": {
                "value": "zephyr_tag_1ad37797-53a4-4af9-a6b4-8b4cf9614e0e",
                "type": "String"
              }
            }
          },
          {
            "name": "LineB/STAGEB",
            "type": "Float",
            "value": 0,
            "properties": {
              "brickClass": {
                "value": "Percent_Load_Parameter",
                "type": "String"
              },
              "cioTags": {
                "value": "zephyr_tag_1ad37797-53a4-4af9-a6b4-8b4cf9614e0e",
                "type": "String"
              }
            }
          },
          {
            "name": "LineA/SYSETA",
            "type": "Float",
            "value": 0,
            "properties": {
              "brickClass": {
                "value": "Return_Water_Temperature_Sensor",
                "type": "String"
              },
              "cioTags": {
                "value": "zephyr_tag_1ad37797-53a4-4af9-a6b4-8b4cf9614e0e",
                "type": "String"
              }
            }
          },
          {
            "name": "LineB/SYSETB",
            "type": "Float",
            "value": 0,
            "properties": {
              "brickClass": {
                "value": "Return_Water_Temperature_Sensor",
                "type": "String"
              },
              "cioTags": {
                "value": "zephyr_tag_1ad37797-53a4-4af9-a6b4-8b4cf9614e0e",
                "type": "String"
              }
            }
          },
          {
            "name": "LineA/SYSLTA",
            "type": "Float",
            "value": 0,
            "properties": {
              "brickClass": {
                "value": "Supply_Water_Temperature_Sensor",
                "type": "String"
              },
              "cioTags": {
                "value": "zephyr_tag_1ad37797-53a4-4af9-a6b4-8b4cf9614e0e",
                "type": "String"
              }
            }
          },
          {
            "name": "LineB/SYSLTB",
            "type": "Float",
            "value": 0,
            "properties": {
              "brickClass": {
                "value": "Supply_Water_Temperature_Sensor",
                "type": "String"
              },
              "cioTags": {
                "value": "zephyr_tag_1ad37797-53a4-4af9-a6b4-8b4cf9614e0e",
                "type": "String"
              }
            }
          },
          {
            "name": "LineA/TCAPA",
            "type": "UInt32",
            "value": {
              "low": 0,
              "high": 0,
              "unsigned": true
            },
            "properties": {
              "brickClass": {
                "value": "Thermal_Energy_Usage_Sensor",
                "type": "String"
              },
              "cioTags": {
                "value": "zephyr_tag_1ad37797-53a4-4af9-a6b4-8b4cf9614e0e",
                "type": "String"
              }
            }
          },
          {
            "name": "LineB/TCAPB",
            "type": "UInt32",
            "value": {
              "low": 0,
              "high": 0,
              "unsigned": true
            },
            "properties": {
              "brickClass": {
                "value": "Thermal_Energy_Usage_Sensor",
                "type": "String"
              },
              "cioTags": {
                "value": "zephyr_tag_1ad37797-53a4-4af9-a6b4-8b4cf9614e0e",
                "type": "String"
              }
            }
          },
          {
            "name": "LineA/TPINPA",
            "type": "UInt32",
            "value": {
              "low": 0,
              "high": 0,
              "unsigned": true
            },
            "properties": {
              "brickClass": {
                "value": "Pump_Electrical_Energy_Usage_Sensor",
                "type": "String"
              },
              "cioTags": {
                "value": "zephyr_tag_1ad37797-53a4-4af9-a6b4-8b4cf9614e0e",
                "type": "String"
              }
            }
          },
          {
            "name": "LineB/TPINPB",
            "type": "UInt32",
            "value": {
              "low": 0,
              "high": 0,
              "unsigned": true
            },
            "properties": {
              "brickClass": {
                "value": "Pump_Electrical_Energy_Usage_Sensor",
                "type": "String"
              },
              "cioTags": {
                "value": "zephyr_tag_1ad37797-53a4-4af9-a6b4-8b4cf9614e0e",
                "type": "String"
              }
            }
          },
          {
            "name": "LineA/TSHFA",
            "type": "Float",
            "value": 0,
            "properties": {
              "brickClass": {
                "value": "Water_Temperature_Setpoint_Shift_Parameter",
                "type": "String"
              },
              "cioTags": {
                "value": "zephyr_tag_1ad37797-53a4-4af9-a6b4-8b4cf9614e0e",
                "type": "String"
              }
            }
          },
          {
            "name": "LineB/TSHFB",
            "type": "Float",
            "value": 0,
            "properties": {
              "brickClass": {
                "value": "Water_Temperature_Setpoint_Shift_Parameter",
                "type": "String"
              },
              "cioTags": {
                "value": "zephyr_tag_1ad37797-53a4-4af9-a6b4-8b4cf9614e0e",
                "type": "String"
              }
            }
          }
        ],
        "seq": 159
      },
      "clientId": "ESP32_05C640",
      "arrivalTime": 1725454459308,
      "traceId": "2b522d17-3602-f5d5-cd03-c82db2c74cea",
      "assetCategory": "ahp"
    }
  }
   
```