import {
  GetObjectCommand,
  PutObjectCommand,
  S3Client,
} from "@aws-sdk/client-s3";
import { getSignedUrl } from "@aws-sdk/s3-request-presigner";
import { SecretManager } from './secret-manager';
import { logger } from "../util/logger";
import { AWSCredentials } from "../models/Credentials";

const s3Client = new S3Client();
let chinaS3Client: S3Client
const secretKey = process.env.CHINA_AWS_CREDS as string;
let awsCredentials: AWSCredentials
export class S3Service {

  public static async putS3Object(
    payload: any,
    fileName: string,
    isChina: boolean = false,
  ): Promise<any> {
    const bucketName = process.env.INTEGRATION_ADAPTER_SPARKPLUG_LOGS_BUCKET_NAME;
    const transformedPayload = payload
    const key = `${fileName}`;

    if (isChina) {
      try {
        const creds: AWSCredentials = await this.getAwsCredentials()
        chinaS3Client = new S3Client({
          region: "cn-northwest-1",
          credentials: {
            accessKeyId: creds.accessKeyId,
            secretAccessKey: creds.secretAccessKey,
          }
        })
      } catch (er) {
        logger.error(JSON.stringify(er));
        return;
      }
    }
    try {
      const params = {
        Bucket: bucketName,
        Key: key,
        Body: JSON.stringify(transformedPayload),
        ContentType: "application/json",
      };

      try {
        isChina ? await chinaS3Client.send(new PutObjectCommand(params)) : await s3Client.send(new PutObjectCommand(params));
        logger.debug(
          `Object '${key}' in bucket '${bucketName}' updated successfully.`
        );
      } catch (error) {
        logger.error(`putS3Object Error updating object '${key}' in bucket '${bucketName}'`, error)
      }
    } catch (error) {
      logger.error(`putS3Object Error updating object '${key}' in bucket '${bucketName}'`, error)
      throw error;
    }
  }

  public static async generateSignedUrl(bucketKey: string): Promise<string> {
    const bucketName = process.env.INTEGRATION_ADAPTER_SPARKPLUG_LOGS_BUCKET_NAME ?? "";
    const key = bucketKey.replace(/_\d+/, "")?.split(" ")?.join("");
    const s3Params = {
      Bucket: bucketName,
      Key: key,
    };

    try {
      const command = new GetObjectCommand(s3Params);
      const url = await getSignedUrl(s3Client, command);

      logger.debug("getObject :: url :: ", url);
      return url;
    } catch (error) {
      logger.error(`generateSignedUrl Error generating signed URL`, error)
      throw error;
    }
  }


  private static async fetchSSMCredentials(): Promise<AWSCredentials> {
    const secretManager = new SecretManager();
    const secretResponse: any = await secretManager.getSecretValue(secretKey);
    awsCredentials = JSON.parse(secretResponse.SecretString) as AWSCredentials;
    return awsCredentials
  }

  private static async getAwsCredentials(): Promise<AWSCredentials> {
    if (awsCredentials?.accessKeyId && awsCredentials?.secretAccessKey) {
      return awsCredentials;
    }
    return await this.fetchSSMCredentials();
  }
}
