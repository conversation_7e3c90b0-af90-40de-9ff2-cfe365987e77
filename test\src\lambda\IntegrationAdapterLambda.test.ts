/* eslint-disable no-undef */
/* eslint-disable sonarjs/no-duplicate-string */
import { Context, Callback } from 'aws-lambda';
import { IntegrationAdapterLambda } from '../../../src/lambda/IntegrationAdapterLambda';
import { IntegrationAdaptertoS3 } from '../../../src/lambda/IntegrationAdaptertoS3';

const mockContext: Context = new (jest.fn<Context, any[]>())();
const mockCallback: Callback = new (jest.fn<Callback, any>())();

const infoLoggerSpy = jest.fn();
const errorLoggerSpy = jest.fn();

jest.mock('@carrier-io/pe-lib-otel-logs', () => ({
  logger: {
    error: (message: string) => errorLoggerSpy(message),
    info: (message: string) => infoLoggerSpy(message),
    debug: (message: string) => infoLoggerSpy(message),
    warn: (message: string) => infoLoggerSpy(message),
    exportLogs: jest.fn(),
  },
}));

jest.mock('../../../src/util/logger', () => ({
  logger: {
    info: jest.fn(),
    error: jest.fn(),
    debug: jest.fn(),
    warn: jest.fn()
  }
}));

// jest.mock('sparkplug-payload', () => {
//   return {
//     get: jest
//       .fn(() => ({}))
//       .mockImplementation(() => {
//         return {
//           decodePayload: jest
//             .fn()
//             .mockReturnValueOnce({
//               timestamp: 1659460457949,
//               metrics: [
//                 {
//                   name: 'ch_ecw',
//                   type: 'Int32',
//                   value: 0,
//                 },
//                 {
//                   name: 'ch_lcw',
//                   type: 'Int32',
//                   value: 0,
//                 },
//               ],
//             })
//             .mockReturnValue({
//               timestamp: 1659460457949,
//               metrics: [
//                 {
//                   name: 'TIMEBCST',
//                   type: 'Int32',
//                   value: 0,
//                   pointId: '557bd4c6-9781-53e4-81cf-36f39959f8d1-f4b5654f',
//                 },
//               ],
//             }),
//         };
//       }),
//   };
// });

const event = {
  Records: [
    {
      kinesis: {
        kinesisSchemaVersion: '1.0',
        partitionKey: 'CCN0',
        sequenceNumber: '49632821948136854700161284884649297177408559840780877842',
        data: '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',
        approximateArrivalTimestamp: 1661841736.739,
      },
      eventSource: 'aws:kinesis',
      eventVersion: '1.0',
      eventID: 'shardId-000000000001:49632821948136854700161284884649297177408559840780877842',
      eventName: 'aws:kinesis:record',
      invokeIdentityArn:
        'arn:aws:iam::550762557643:role/test-Zephyr-Pipeline-Cons-carrieriozephyrpipelinec-H4H05YR84M7R',
      awsRegion: 'us-east-1',
      eventSourceARN:
        'arn:aws:kinesis:us-east-1:550762557643:stream/carrier-io-zephyr-pipeline-consumer-StateService-test',
    },
    // {
    //   kinesis: {
    //     kinesisSchemaVersion: '1.0',
    //     partitionKey: 'CCN0',
    //     sequenceNumber: '49632821948136854700161284884649297177408559840780877842',
    //     data: 'ewoJInNwYXJrcGx1ZyI6ICJ7XCJ0aW1lc3RhbXBcIjoxNjU5NDYwNDU3OTQ5LFwibWV0cmljc1wiOlt7XCJuYW1lXCI6XCJUSU1FQkNTVFwiLFwidHlwZVwiOlwiSW50MzJcIixcInZhbHVlXCI6MH1dfSIsCgkiY2xpZW50SWQiOiAiY2FycmllcmlvLjkwMmUxNjhhYzBkNiIsCgkiYXJyaXZhbFRpbWUiOiAiVHVlIEF1ZyAwMiAyMDIyIDE3OjE0OjM2IEdNVCswMDAwIChDb29yZGluYXRlZCBVbml2ZXJzYWwgVGltZSkiLAoJInRvcGljIjogInNwQnYxLjAvemVwaHlyL0REQVRBLzkwMmUxNjhhYzBkNi9DQ04wIgp9',
    //     approximateArrivalTimestamp: 1661841736.739,
    //   },
    //   eventSource: 'aws:kinesis',
    //   eventVersion: '1.0',
    //   eventID: 'shardId-000000000001:49632821948136854700161284884649297177408559840780877842',
    //   eventName: 'aws:kinesis:record',
    //   invokeIdentityArn:
    //     'arn:aws:iam::550762557643:role/test-Zephyr-Pipeline-Cons-carrieriozephyrpipelinec-H4H05YR84M7R',
    //   awsRegion: 'us-east-1',
    //   eventSourceARN:
    //     'arn:aws:kinesis:us-east-1:550762557643:stream/carrier-io-zephyr-pipeline-consumer-StateService-test',
    // },
    // {
    //   kinesis: {
    //     kinesisSchemaVersion: '1.0',
    //     partitionKey: 'CCN0',
    //     sequenceNumber: '49632821948136854700161284884649297177408559840780877842',
    //     data: 'ewoJInNwYXJrcGx1ZyI6IHt9LAoJImNsaWVudElkIjogImNhcnJpZXJpby45MDJlMTY4YWMwZDYiLAoJImFycml2YWxUaW1lIjogIlR1ZSBBdWcgMDIgMjAyMiAxNzoxNDozNiBHTVQrMDAwMCAoQ29vcmRpbmF0ZWQgVW5pdmVyc2FsIFRpbWUpIiwKCSJ0b3BpYyI6ICJzcEJ2MS4wL3plcGh5ci9EQklSVEgvOTAyZTE2OGFjMGQ2L0NDTjAiCn0=',
    //     approximateArrivalTimestamp: 1661841736.739,
    //   },
    //   eventSource: 'aws:kinesis',
    //   eventVersion: '1.0',
    //   eventID: 'shardId-000000000001:49632821948136854700161284884649297177408559840780877842',
    //   eventName: 'aws:kinesis:record',
    //   invokeIdentityArn:
    //     'arn:aws:iam::550762557643:role/test-Zephyr-Pipeline-Cons-carrieriozephyrpipelinec-H4H05YR84M7R',
    //   awsRegion: 'us-east-1',
    //   eventSourceARN:
    //     'arn:aws:kinesis:us-east-1:550762557643:stream/carrier-io-zephyr-pipeline-consumer-StateService-test',
    // },
  ],
};

describe('Test Integration Adapter Lambda Function', () => {
  beforeEach(() => {});

  afterEach(() => {
    jest.clearAllMocks();
  });

  test('Test Integration Adapter for DBIRTH message', async () => {
    jest.spyOn(console, 'info').mockImplementation();

    // mock Metadata Lookup Service

    await IntegrationAdapterLambda.handler(event, mockContext, mockCallback);
    // expect(console.info).toHaveBeenCalledTimes(3);
  });
  test('Test Integration Adapter for LWT Message', async () => {
    jest.spyOn(console, 'info').mockImplementation();
    const lwtEvent = {
      Records: [
        {
          kinesis: {
            kinesisSchemaVersion: '1.0',
            partitionKey: 'CCN0',
            sequenceNumber: '49632821948136854700161284884649297177408559840780877842',
            data: 'ewogICAgInNwYXJrcGx1ZyI6ICJNUT09IiwKICAgICJjbGllbnRJZCI6ICJNSjBKNzAxNyIsCiAgICAiYXJyaXZhbFRpbWUiOiAxNjk2MjU2NTE4NzEzLAogICAgInRvcGljIjogInN0YXR1cy9haHAvY29ubmVjdGlvbi9NSjBKNzAxNyIsCiAgICAidHJhY2VJZCI6ICI0Yzg3NzIzYy0zZmYzLWM5MTYtMzE5Zi00YTA3NDhmNWQ0NGQiCn0=',
            approximateArrivalTimestamp: 1661841736.739,
          },
          eventSource: 'aws:kinesis',
          eventVersion: '1.0',
          eventID: 'shardId-000000000001:49632821948136854700161284884649297177408559840780877842',
          eventName: 'aws:kinesis:record',
          invokeIdentityArn:
            'arn:aws:iam::550762557643:role/test-Zephyr-Pipeline-Cons-carrieriozephyrpipelinec-H4H05YR84M7R',
          awsRegion: 'us-east-1',
          eventSourceARN:
            'arn:aws:kinesis:us-east-1:550762557643:stream/carrier-io-zephyr-pipeline-consumer-StateService-test',
        },
      ],
    };
    await IntegrationAdapterLambda.handler(lwtEvent, mockContext, mockCallback);
  });

  test('Test IntegrationAdaptertoS3', async () => {
    jest.spyOn(console, 'info').mockImplementation();

    // mock Metadata Lookup Service

    await IntegrationAdaptertoS3.handler(event, mockContext, mockCallback);
    // expect(console.info).toHaveBeenCalledTimes(3);
  });
});
