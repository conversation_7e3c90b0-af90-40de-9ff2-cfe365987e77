const TOPIC_SEPARATOR_CHAR = '/';

/**
 * Structure of the MQTT Sparkplug topic.
 * {schema}/{groupId}/{messageType}/{edgeNodeId}/{deviceId}
 */
export type SparkplugTopicParts = {
  schema: string;
  groupId: string;
  messageType: string;
  edgeNodeId: string;
  deviceId: string;
};

/**
 * Parses topic string to the object
 * @param topic - Sparkplug topic raw string
 */
export const fromString = (topic: string): SparkplugTopicParts => {
  const topicParts = topic.split(TOPIC_SEPARATOR_CHAR);
  return {
    schema: topicParts[0],
    groupId: topicParts[1],
    messageType: topicParts[2],
    edgeNodeId: topicParts[3],
    deviceId: topicParts[4] ?? '',
  };
};
