import { SparkplugKinesisRecord } from '../models/SparkplugKinesisRecord';
import { getFileName, logger } from '../util/';
import { BaseIntegrationAdapterLambda } from './BaseIntegrationAdapterLambda';
import { S3Service } from '../services/s3-service';

export class IntegrationAdaptertoS3 extends BaseIntegrationAdapterLambda {
    protected static override readonly logRequest: boolean = false;

    protected static override async onMessage(message: SparkplugKinesisRecord) {
        try {
            logger.info('Message received for topic-', message.topic);

            const fileName = getFileName(message.topic)

            if (fileName) {
                logger.info('fileName', fileName);

                await S3Service.putS3Object({
                    clientId: message.clientId,
                    arrivalTime: message.arrivalTime,
                    topic: message.topic,
                    sparkplugMessage: message.sparkplugMessage,
                    traceId: message.traceId,
                    rawSparkplugMessage: message.rawSparkplugMessage || '',
                },
                    fileName, /* isChina */ false);
            }

            // set true if this is for China region

        } finally {
            await logger.exportLogs();
        }
    }


    protected static override async onParseError(error: Error, rawMessage: string) {
        try {
            logger.error(`[ON PARSE ERROR] Could not parse record into sparkplug: ${rawMessage}`, error);
        } finally {
            await logger.exportLogs();
        }
    }

}
