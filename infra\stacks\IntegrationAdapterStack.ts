/* eslint-disable no-new */
import { BasicNodeJsFunction, ConfigManifest } from '@carrier/backend-lib-infrastructure';
import {
  aws_s3 as S3,
  Stack,
  StackProps,
  aws_iot as Iot,
  aws_kinesis as Kinesis,
  aws_lambda_event_sources as LambdaEventSource,
  aws_lambda as Lambda,
  aws_iam as Iam,
  Fn,
  Stage,
  RemovalPolicy,
} from 'aws-cdk-lib';
import { LogRetention } from 'aws-cdk-lib/aws-logs';
import { DeployTimeRegistryService } from '@carrier-io/backend-lib-registry-service';
import { Config } from '../config';

export class IntegrationAdapterStack extends Stack {
  public adapterStream: Kinesis.Stream;

  public streamConsumer: Kinesis.CfnStreamConsumer;

  private lambdaFunction: BasicNodeJsFunction;

  private targetStreams: Kinesis.IStream[] = [];

  private registry: DeployTimeRegistryService;

  private config: ReturnType<typeof Config.from>;

  private cjcStaticMappingBucket: S3.Bucket;
  public writeToKinesisRole: Iam.Role;

  public constructor(scope: Stage, manifest: ConfigManifest, id: string, props?: StackProps) {
    super(scope, id, props);
    this.registry = new DeployTimeRegistryService();
    this.config = Config.from(manifest, this.partition, scope.stageName, this.region, this.account);

    this.createS3Bucket();
    this.setupAdapterStream();
    this.attachAdapterStreamToDataSources();
    this.createLambdaConsumer();
    this.givePermissionsToTargetStreams();
    this.addDlqPolicy();

    const baseKey = `dlq/destination/${Config.sourceId}`;
    this.registry.register({
      scope: this,
      key: Config.isPersonalEnvironment ? ConfigManifest.applyNameSpace(baseKey, scope.stageName) : baseKey,
      value: 'main-pipeline',
    });
  }

  private setupAdapterStream() {
    this.adapterStream = new Kinesis.Stream(this, 'adapter-kinesis-stream', this.config.kinesis.adapterStream);
  }

  private attachAdapterStreamToDataSources() {
    this.writeToKinesisRole = this.createAdapterStreamRole(this.config.kinesis.adapterStream.roleName);

    this.config.iotCoreRules.forEach((rule) => {
      new Iot.CfnTopicRule(this, rule.ruleName, {
        topicRulePayload: {
          actions: [
            {
              kinesis: {
                roleArn: this.writeToKinesisRole.roleArn,
                streamName: this.adapterStream.streamName,
                partitionKey: rule.kinesisPartitionKey,
              },
            },
          ],
          awsIotSqlVersion: rule.awsIotSqlVersion,
          sql: rule.sql,
        },
        ruleName: rule.ruleName,
      });
    });
  }

  private createS3Bucket() {
    const bucket = new S3.Bucket(this, `integration-adapter-cjc-mapping-bucket`, {
      bucketName: `${this.config.s3Bucket.bucketName}`,
      removalPolicy: RemovalPolicy.DESTROY,
      blockPublicAccess: S3.BlockPublicAccess.BLOCK_ALL,
      autoDeleteObjects: true,
    });

    this.cjcStaticMappingBucket = bucket;
  }

  private createLambdaConsumer() {
    const { logRetentionId, logGroupName, retention } = this.config.lambdaFunction.logs;
    const _logRetention = new LogRetention(this, logRetentionId, { logGroupName, retention });

    this.lambdaFunction = new BasicNodeJsFunction(
      this,
      this.config.lambdaFunction.properties.functionName,

      {
        ...this.config.lambdaFunction.properties,
        environment: {
          ...this.config.lambdaFunction.properties.environment,
          S3_BUCKET: this.cjcStaticMappingBucket.bucketName,
          S3_MAPPING_FILEPATH: 'Constants/CCNMapping.json',
        },
      },
    );

    this.configureOTELLogLibraryLambdaLayer(this.lambdaFunction);
    this.grantLambdaAccessToExecuteOtelLayer(this.lambdaFunction);

    this.cjcStaticMappingBucket.grantRead(this.lambdaFunction);

    if (this.config.lambdaFunction.isEnhancedFanout) {
      this.streamConsumer = new Kinesis.CfnStreamConsumer(this, 'adapter-stream-fan-out-consumer', {
        consumerName: `${this.config.lambdaFunction.properties.functionName}-fanout`,
        streamArn: this.adapterStream.streamArn,
      });
      this.streamConsumer = new Kinesis.CfnStreamConsumer(this, 'adapter-stream-fan-out-consumer', {
        consumerName: `${this.config.lambdaFunction.properties.functionName}-fanout`,
        streamArn: this.adapterStream.streamArn,
      });

      new Lambda.EventSourceMapping(this, 'adapter-stream-fan-out-conumser-event-source-mapping', {
        ...this.config.lambdaFunction.eventSource,
        eventSourceArn: this.streamConsumer.attrConsumerArn,
        target: this.lambdaFunction,
      });

      this.createFanoutPolicy();
    } else {
      // attach kinesis event source
      this.lambdaFunction.addEventSource(
        new LambdaEventSource.KinesisEventSource(this.adapterStream, this.config.lambdaFunction.eventSource),
      );
    }
  }

  private configureOTELLogLibraryLambdaLayer(lambdaInstance: BasicNodeJsFunction) {
    const otelLayer = Lambda.LayerVersion.fromLayerVersionArn(
      this,
      `otel-initializer-${lambdaInstance.functionName}`,
      `arn:${this.partition}:lambda:${this.config.otelLogServiceAccountRegion}:${this.config.otelLogServiceAccount}:layer:otel-initializer:${this.config.otelLogServiceAccountLambdaVersion}`,
    );
    lambdaInstance.addLayers(otelLayer);
  }

  private grantLambdaAccessToExecuteOtelLayer(lambdaInstance: BasicNodeJsFunction) {
    lambdaInstance.addToRolePolicy(
      new Iam.PolicyStatement(
        new Iam.PolicyStatement({
          effect: Iam.Effect.ALLOW,
          actions: ['ssm:GetParameter*'],
          resources: ['*'],
        }),
      ),
    );
  }

  private createAdapterStreamRole(name: string): Iam.Role {
    const role = new Iam.Role(this, name, {
      assumedBy: new Iam.ServicePrincipal('iot.amazonaws.com'),
      roleName: name,
    });

    role.addToPolicy(
      new Iam.PolicyStatement({
        resources: [this.adapterStream.streamArn],
        actions: ['kinesis:PutRecord'],
      }),
    );

    role.addToPolicy(
      new Iam.PolicyStatement({
        actions: ['s3:PutObject', 's3:GetObject'],
        resources: [`${this.cjcStaticMappingBucket.bucketArn}/*`],
      }),
    );

    return role;
  }

  private createFanoutPolicy() {
    const kinesisStreamReadPolicyStmt = new Iam.PolicyStatement({
      resources: [this.adapterStream.streamArn],
      actions: [
        'kinesis:DescribeStreamSummary',
        'kinesis:GetRecords',
        'kinesis:GetShardIterator',
        'kinesis:ListShards',
      ],
    });

    const kinesisConsumerPolicyStmt = new Iam.PolicyStatement({
      resources: [this.streamConsumer.attrConsumerArn],
      actions: ['kinesis:SubscribeToShard'],
    });

    this.lambdaFunction.addToRolePolicy(kinesisStreamReadPolicyStmt);
    this.lambdaFunction.addToRolePolicy(kinesisConsumerPolicyStmt);
  }

  private givePermissionsToTargetStreams() {
    for (const [name, arn] of Object.entries(this.config.kinesis.targetStreams)) {
      if (arn) {
        const stream = Kinesis.Stream.fromStreamArn(this, `target-stream-${name}`, arn);

        this.targetStreams.push(stream);

        stream.grantWrite(this.lambdaFunction);
      }
    }
  }

  private addDlqPolicy() {
    const dlqApiGwAppId = Fn.importValue(this.config.dlqAPIGwAppId);

    this.lambdaFunction.addToRolePolicy(
      new Iam.PolicyStatement({
        effect: Iam.Effect.ALLOW,
        actions: ['execute-api:Invoke'],
        resources: [`arn:${this.partition}:execute-api:${this.region}:${this.account}:${dlqApiGwAppId}/*`],
      }),
    );
  }
}
