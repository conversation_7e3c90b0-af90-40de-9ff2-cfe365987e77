{"extends": "@tsconfig/node18/tsconfig.json", "compilerOptions": {"declaration": true, "strict": true, "noImplicitAny": false, "strictNullChecks": true, "noImplicitThis": true, "alwaysStrict": true, "noUnusedLocals": false, "noUnusedParameters": false, "noImplicitReturns": true, "noFallthroughCasesInSwitch": false, "sourceMap": true, "inlineSources": true, "experimentalDecorators": true, "strictPropertyInitialization": false, "types": ["node", "jest"], "outDir": "dist", "baseUrl": "src"}, "include": ["src/**/*.ts", "src/**/*.js"], "exclude": ["cdk.out"]}