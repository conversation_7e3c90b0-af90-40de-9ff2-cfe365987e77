# CJC Integration Adapter Contributing Guide

## Contents

[TOC]

## Getting Started

This guide contains the necessary instructions for developers who will contribute to the CJC integration adapter. This Contributing Guide provides instructions for working in this repository. It also contains links to the architecture, an overview of the project structure, and details about any prerequisites required. For a more detailed overview and links to related documentation, see the [ReadMe file](https://bitbucket.org/carrier-digital/ahp-integrationadapter-cjc/src/main/README.md).  

### Prerequisites

#### Tooling

* [Node.js 16](https://github.com/nvm-sh/nvm)
* [Yarn](https://yarnpkg.com/getting-started/install)
* [Docker](https://docs.docker.com/get-docker/)
* [AWS CDK](https://docs.aws.amazon.com/cdk/v2/guide/getting_started.html#getting_started_install)

#### AWS Credentials
Accounts that we work with:

* Carrier.IO Dev: ************
* Carrier.IO QA: ************

To work on the project, you need AWS credentials configured in your terminal. There are at least two ways of doing that.

##### SSO Login

One way is to use SSO.

1. Add these profiles to your `~/.aws/config`

    ```ini
    [profile default]
    sso_start_url = https://carrier-aws-cloud.awsapps.com/start/#
    sso_region = us-east-1
    sso_account_id = ************
    sso_role_name = IoTDevPreProdAccess
    region = us-east-1
    output = json

    [profile qa]
    sso_start_url = https://carrier-aws-cloud.awsapps.com/start/#
    sso_region = us-east-1
    sso_account_id = ************
    sso_role_name = PowerUserIAM
    region = us-east-1
    output = json
    
    ```


2. Add `awslogin` function to your `~/.profile`

    ```bash
    function awslogin() {
        aws sso login --profile "${1:-default}"
    }
    ```

3. Try it out

    ```bash
    awslogin
    # the account id in the output should be ************
    aws sts get-caller-identity
    aws s3 ls
    ```

For more details, see [AWS Login](https://carrier-digital.atlassian.net/wiki/spaces/IO/pages/*********/AWS+Login).

##### Copy Credentials

Another way is to copy credentials from https://carrier-aws-cloud.awsapps.com and then paste them into the terminal. Set the region to `us-east-1`. It should look something like this:

```
export AWS_ACCESS_KEY_ID="example"
export AWS_SECRET_ACCESS_KEY="example"
export AWS_SESSION_TOKEN="example"
export AWS_REGION="us-east-1"
```

## Architecture

[ Solution Architecture on Confluence ](https://carrier-digital.atlassian.net/wiki/spaces/IO/pages/**********/)

## Multi-account/Multi-region Deployment

This gives the details for multi-account and multi-region deployment of the services.

[Utilize Manifest for Multi-tenancy](https://carrier-digital.atlassian.net/wiki/spaces/IO/pages/**********/Multi-Tenancy+for+Repositories)


## Project Structure
* `infra/` - Infrastructure code. Configuration and application creation are located here.
* `docs/` - Documentation folder that includes markdown generated via TypeDoc (classes, enums, interfaces, etc). The Docs folder ReadMe should be an autogenerated index of these files.
* `src/` - Main folder for developers. All lamdas are located here.

## Instructions


### How to Install

1. Run `yarn rc` using your AWS credentials
2. Run `yarn install`

### How to Format

Formatting is based on [prettier](https://prettier.io/) and can be run via the following command: `yarn prettier`

### How to Generate Type Documentation

Typedoc will be installed as a dependency under How to Install. Once that is done, just follow the instructions in this section.

You can generate type documentation files with [Typedoc](https://github.com/TypeStrong/typedoc) via the following command:

```shell
npm run gen:typedoc
```

Or

```shell
yarn gen:typedoc
```


### How to Test

```shell
yarn test
```

### How to Deploy

1. You can deploy this infrastructure cdk manually via the following command:

    ```shell
       npx cdk synth -c stage=<environment>
       npx cdk deploy -c stage=<environment> <environment>/*
    ```
    
2. [CD/CI Pipeline](https://carrier-digital.atlassian.net/wiki/spaces/IO/pages/**********/Carrier+CI+CD+Pipelines)
      - On Dev Env(`IoT_Carrier_IO_Dev`): When merged into the main branch, the CD/CI Pipeline will publish this module on the AWS DEV Account.  
      - On PreProd Env(`IoT_Carrier_IO_PreProd`): When adding a PreProd release tag `<version>-preprod+<pipeline_number>.<commit_hash>` or patch tag `rc-patch-<version>`.  
          - EX: `0.0.1-preprod+001.0c15c80` or `rc-patch-0.0.1`  
      - On Prod Env(IoT_Carrier_IO_Prod): When adding a Prod release tag `<version>-prod+<pipeline_number>.<commit_hash>`.  
          - EX: `0.0.1-prod+001.0c15c80`  