import { SecretsManagerClient, GetSecretValueCommand, GetSecretValueResponse } from '@aws-sdk/client-secrets-manager';
import { SecretManager } from '../../../src/services/secret-manager';

jest.mock('@aws-sdk/client-secrets-manager');

describe('SecretManager', () => {
  let secretManager: SecretManager;
  let mockSend: jest.Mock;

  beforeEach(() => {
    secretManager = new SecretManager();
    mockSend = jest.fn();
    SecretsManagerClient.prototype.send = mockSend;
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should return secret value successfully', async () => {
    const secretId = 'test-secret-id';
    const mockResponse: GetSecretValueResponse = {
      SecretString: 'my-secret-value'
    };

    mockSend.mockResolvedValue(mockResponse);

    const result = await secretManager.getSecretValue(secretId);

    expect(mockSend).toHaveBeenCalledWith(expect.any(GetSecretValueCommand));
    expect(result).toEqual(mockResponse);
  });

  it('should handle errors gracefully', async () => {
    const secretId = 'test-secret-id';
    const mockError = new Error('Secrets Manager Error');

    mockSend.mockRejectedValue(mockError);

    await expect(secretManager.getSecretValue(secretId)).rejects.toThrow('Secrets Manager Error');
    expect(mockSend).toHaveBeenCalledWith(expect.any(GetSecretValueCommand));
  });
});
