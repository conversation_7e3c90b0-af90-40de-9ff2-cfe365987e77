import { S3Service } from "../../../src/services/s3-service";
import { S3Client, PutObjectCommand } from "@aws-sdk/client-s3";
import { getSignedUrl } from "@aws-sdk/s3-request-presigner";

jest.mock("@aws-sdk/s3-request-presigner", () => ({
  getSignedUrl: jest.fn(),
}));

jest.mock("@aws-sdk/client-s3", () => {
  const originalModule = jest.requireActual("@aws-sdk/client-s3");
  return {
    ...originalModule,
    PutObjectCommand: jest.fn().mockImplementation((input) => ({
      input,
    })),
  };
});

jest.mock("../../../src/services/secret-manager", () => {
  return {
    SecretManager: jest.fn(), // We'll override this dynamically in tests
  };
});

const mockSend = jest.fn();
S3Client.prototype.send = mockSend;

describe("Test S3 Service -- putS3Object", () => {
  beforeEach(() => {
    mockSend.mockReset();

    // Reset SecretManager to return valid credentials by default
    const { SecretManager } = require("../../../src/services/secret-manager");
    SecretManager.mockImplementation(() => ({
      getSecretValue: jest.fn().mockResolvedValue({
        SecretString: JSON.stringify({
          accessKeyId: "mockAccessKey",
          secretAccessKey: "mockSecretKey",
        }),
      }),
    }));
  });

  it("Should upload object to correct S3 key", async () => {
    mockSend.mockResolvedValue({});

    const payload = {
      data: "test-value",
    };

    process.env.INTEGRATION_ADAPTER_SPARKPLUG_LOGS_BUCKET_NAME = "my-test-bucket";

    await S3Service.putS3Object(payload, "factory/machineA/123.json");

    expect(mockSend).toHaveBeenCalledTimes(1);

    expect(PutObjectCommand).toHaveBeenCalledWith({
      Bucket: "my-test-bucket",
      Key: "factory/machineA/123.json",
      Body: JSON.stringify(payload),
      ContentType: "application/json",
    });
  });

  it("Should handle errors when S3 putObject fails", async () => {
    mockSend.mockRejectedValue(new Error("Upload failed"));

    const payload = {};

    process.env.MASTER_MODEL_BUCKET_NAME = "my-test-bucket";

    await expect(S3Service.putS3Object(payload, "factory/machineB/456.json")).resolves.toBeUndefined();

    expect(mockSend).toHaveBeenCalledTimes(1);
  });

  it("Should use China S3 client when isChina is true", async () => {
    mockSend.mockResolvedValue({});

    process.env.INTEGRATION_ADAPTER_SPARKPLUG_LOGS_BUCKET_NAME = "china-bucket";
    process.env.CHINA_AWS_CREDS = "mock-secret-key";

    const payload = { message: "hello china" };

    await S3Service.putS3Object(payload, "china/file.json", true);

    expect(mockSend).toHaveBeenCalledTimes(1);
    expect(PutObjectCommand).toHaveBeenCalledWith({
      Bucket: "china-bucket",
      Key: "china/file.json",
      Body: JSON.stringify(payload),
      ContentType: "application/json",
    });
  });

  it("Should log error if fetching China credentials fails", async () => {
    const { SecretManager } = require("../../../src/services/secret-manager");
    SecretManager.mockImplementation(() => ({
      getSecretValue: jest.fn().mockRejectedValue(new Error("Secret fetch failed")),
    }));

    process.env.INTEGRATION_ADAPTER_SPARKPLUG_LOGS_BUCKET_NAME = "china-bucket";
    process.env.CHINA_AWS_CREDS = "mock-secret-key";

    const payload = { msg: "fail credentials" };

    await S3Service.putS3Object(payload, "fail.json", true);
  });


  it("Should generate signed URL from key", async () => {
    process.env.INTEGRATION_ADAPTER_SPARKPLUG_LOGS_BUCKET_NAME = "signed-url-bucket";

    const fakeUrl = "https://signed-url.com/object.json";
    (getSignedUrl as jest.Mock).mockResolvedValue(fakeUrl);

    const url = await S3Service.generateSignedUrl("some_key_123");

    expect(getSignedUrl).toHaveBeenCalled();
    expect(url).toBe(fakeUrl);
  });
});
