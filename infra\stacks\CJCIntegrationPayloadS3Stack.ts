import { BasicNodeJsFunction, ConfigManifest } from "@carrier/backend-lib-infrastructure";
import {
    Stack, StackProps,
    aws_iot as Iot,
    aws_kinesis as Kines<PERSON>,
    aws_lambda_event_sources as LambdaEventSource,
    aws_lambda as Lambda,
    aws_iam as Iam,
    Stage,
    aws_s3 as S3,
    RemovalPolicy,
} from "aws-cdk-lib";
import { Config } from "../config";
import { Helper } from '../helper';

export class CJCIntegrationPayloadS3Stack extends Stack {
    private scope: Stage;
    private stageName: string;

    private adapterStream: Kinesis.Stream;

    private streamConsumer: Kinesis.CfnStreamConsumer;

    private lambdaFunction: BasicNodeJsFunction;

    private config: ReturnType<typeof Config.from>;
    private rawLogBucket: S3.Bucket;

    public constructor(scope: Stage, manifest: ConfigManifest, id: string, adapterStream: Kinesis.Stream, streamConsumer: Kinesis.CfnStreamConsumer, props?: StackProps) {
        super(scope, id, props);
        this.adapterStream = adapterStream
        this.streamConsumer = streamConsumer
        this.config = Config.from(manifest, this.partition, scope.stageName, this.region, this.account);
        this.stageName = scope.stageName ?? 'dev';
        this.scope = scope
        this.createLambdaConsumer();
        this.createRawLogBucket();
    }

    private configureOTELLogLibraryLambdaLayer(lambdaInstance: BasicNodeJsFunction) {
        const otelLayer = Lambda.LayerVersion.fromLayerVersionArn(
            this,
            `otel-initializer-${lambdaInstance.functionName}`,
            `arn:${this.partition}:lambda:${this.config.otelLogServiceAccountRegion}:${this.config.otelLogServiceAccount}:layer:otel-initializer:${this.config.otelLogServiceAccountLambdaVersion}`,
        );
        lambdaInstance.addLayers(otelLayer);
    }

    private grantLambdaAccessToExecuteOtelLayer(lambdaInstance: BasicNodeJsFunction) {
        lambdaInstance.addToRolePolicy(
            new Iam.PolicyStatement(
                new Iam.PolicyStatement({
                    effect: Iam.Effect.ALLOW,
                    actions: ['ssm:GetParameter*'],
                    resources: ['*'],
                }),
            ),
        );
    }
    private createLambdaConsumer() {

        this.lambdaFunction = new BasicNodeJsFunction(
            this,
            ConfigManifest.applyNameSpace(
                `carrier-io-cjc-integration-adapter-logs-to-s3`,
                this.stageName,
            ),
            {
                ...this.config.lambdaFunction.properties,
                entry: `${__dirname}/../../src/lambda/IntegrationAdaptertoS3.ts`,
                handler: 'index.IntegrationAdaptertoS3.handler',
                functionName: ConfigManifest.applyNameSpace(
                    `carrier-io-cjc-integration-adapter-logs-to-s3`,
                    this.stageName,
                ),
                environment: {
                    ...this.config.lambdaFunction.properties.environment,
                    INTEGRATION_ADAPTER_SPARKPLUG_LOGS_BUCKET_NAME: ConfigManifest.applyNameSpace(`cjc-message-payloads`, this.stageName),
                }
            }
        );

        this.configureOTELLogLibraryLambdaLayer(this.lambdaFunction);
        this.grantLambdaAccessToExecuteOtelLayer(this.lambdaFunction);
        // attach kinesis event source

        if (this.config.lambdaFunction.isEnhancedFanout) {
            new Lambda.EventSourceMapping(this, 'adapter-stream-fan-out-conumser-event-source-mapping', {
                ...this.config.lambdaFunction.eventSource,
                eventSourceArn: this.streamConsumer.attrConsumerArn,
                target: this.lambdaFunction,
            });

            this.createFanoutPolicy();
        } else {
            // attach kinesis event source
            this.lambdaFunction.addEventSource(
                new LambdaEventSource.KinesisEventSource(this.adapterStream, this.config.lambdaFunction.eventSource),
            );
        }

    }

    private createFanoutPolicy() {
        const kinesisStreamReadPolicyStmt = new Iam.PolicyStatement({
            resources: [this.adapterStream.streamArn],
            actions: [
                'kinesis:DescribeStreamSummary',
                'kinesis:GetRecords',
                'kinesis:GetShardIterator',
                'kinesis:ListShards',
            ],
        });

        const kinesisConsumerPolicyStmt = new Iam.PolicyStatement({
            resources: [this.streamConsumer.attrConsumerArn],
            actions: ['kinesis:SubscribeToShard'],
        });

        this.lambdaFunction.addToRolePolicy(kinesisStreamReadPolicyStmt);
        this.lambdaFunction.addToRolePolicy(kinesisConsumerPolicyStmt);
    }

    private createRawLogBucket() {
        this.rawLogBucket = new S3.Bucket(this, 'cjcmessagepayloads', {
            blockPublicAccess: S3.BlockPublicAccess.BLOCK_ALL,
            removalPolicy: Helper.isProduction(this.scope) ? RemovalPolicy.RETAIN : RemovalPolicy.DESTROY,
            autoDeleteObjects: !Helper.isProduction(this.scope),
            bucketName: ConfigManifest.applyNameSpace('cjc-message-payloads', this.stageName),
            versioned: true,
        });

        // Create a new policy statement for the Lambda function to perform getObject
        const s3ObjectPolicy = new Iam.PolicyStatement({
            effect: Iam.Effect.ALLOW,
            principals: [new Iam.ArnPrincipal(this.lambdaFunction.role!.roleArn)],
            actions: ['s3:PutObject'],
            resources: [this.rawLogBucket.arnForObjects('*')],
        });

        // ListBucket permission should be applied at the bucket level, not on individual objects
        const s3BucketPolicy = new Iam.PolicyStatement({
            effect: Iam.Effect.ALLOW,
            principals: [new Iam.ArnPrincipal(this.lambdaFunction.role!.roleArn)],
            actions: ['s3:ListBucket'],
            resources: [this.rawLogBucket.bucketArn],
        })

        // Add the policy statement to the bucket policy
        // this.rawLogBucket.PutObjectAcl(this.lambdaFunction)
        this.rawLogBucket.addToResourcePolicy(s3ObjectPolicy);
        this.rawLogBucket.addToResourcePolicy(s3BucketPolicy);
    }
}