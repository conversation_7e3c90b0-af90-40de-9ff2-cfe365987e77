import { addCioTagAndUpdateCcnPoints, isLwtRecord, getFileName } from '../../../src/util/helper';

const sampleMetrics = [
  {
    name: "Chiller/softwareVersion",
    type: "String",
    value: "FFFFFFFF",
    properties: {
      brickClass: {
        value: "Software Version",
        type: "String"
      }
    }
  },
  {
    name: "CircuitD/CDSGP",
    type: "Float",
    value: 0,
    properties: {
      brickClass: {
        value: "CircuitD_Suction_Pressure_Sensor",
        type: "String"
      },
      hasUnit: {
        value: "MegaPA",
        type: "String"
      }
    }
  }
];
describe('Helper Functions', () => {
  
  describe('isLwtRecord', () => {
    it('should return true for valid LWT status/connection topic with value 1', () => {
      const topic = 'STATUS/device1/CONNECTION';
      const payload = {
        sparkplug: Buffer.from('1').toString('base64')
      };
      
      expect(isLwtRecord(topic, payload)).toBe(true);
    });

    it('should return true for valid LWT status/connection topic with value 0', () => {
      const topic = 'STATUS/device1/CONNECTION';
      const payload = {
        sparkplug: Buffer.from('0').toString('base64')
      };
      
      expect(isLwtRecord(topic, payload)).toBe(true);
    });

    it('should return false for invalid topic format', () => {
      const topic = 'WRONG/device1/TOPIC';
      const payload = {
        sparkplug: Buffer.from('1').toString('base64')
      };
      
      expect(isLwtRecord(topic, payload)).toBe(false);
    });
  });

  describe('addCioTagAndUpdateCcnPoints', () => {
    
  
    test('should add cioTags for DBIRTH topic', () => {
      const topic = 'DBIRTH';
      const cioTag = 'testCioTag';
      const result = addCioTagAndUpdateCcnPoints(sampleMetrics, topic, cioTag);
      
      result.forEach(metric => {
        expect(metric.properties.cioTags).toEqual({
          value: 'testCioTag',
          type: 'String'
        });
      });
    });
  
    test('should add cioTags for DDATA topic', () => {
      const topic = 'DDATA';
      const cioTag = 'testCioTag';
      const result = addCioTagAndUpdateCcnPoints(sampleMetrics, topic, cioTag);
      
      result.forEach(metric => {
        expect(metric.properties.cioTags).toEqual({
          value: 'testCioTag',
          type: 'String'
        });
      });
    });
  
  
    test('should handle metrics without properties', () => {
      const metricsWithoutProperties = [
        {
          name: "Test",
          type: "String",
          value: "test"
        }
      ];
      const topic = 'DBIRTH';
      const cioTag = 'testCioTag';
      
      const result = addCioTagAndUpdateCcnPoints(metricsWithoutProperties, topic, cioTag);
      expect(result).toEqual(metricsWithoutProperties);
    });
  
    test('should not modify original metrics array', () => {
      const topic = 'DBIRTH';
      const cioTag = 'testCioTag';
      const originalMetrics = [...sampleMetrics];
      
      addCioTagAndUpdateCcnPoints(sampleMetrics, topic, cioTag);
      expect(sampleMetrics).toEqual(originalMetrics);
    });
    test('should update ccnPoint', () => {

      const topic = 'DBIRTH';
      const cioTag = 'testCioTag';
      
      const result = addCioTagAndUpdateCcnPoints(sampleMetrics, topic, cioTag);
      expect(result[0].name).toBe('softwareVersion');
      expect(result[1].name).toBe('CDSGP')
    });
  });
});

describe('getFileName', () => {
  const fixedTimestamp = 1720000000000;

  beforeAll(() => {
    jest.spyOn(Date, 'now').mockReturnValue(fixedTimestamp);
  });

  afterAll(() => {
    jest.restoreAllMocks();
  });

  it('should generate filename with chiller', () => {
    const topic = 'svBv1.0/ahp/DDATA/TPAD679158/EconomizerA-2922Q98297';
    const result = getFileName(topic);

    expect(result).toBe(`ahp/TPAD679158/EconomizerA-2922Q98297/DDATA/${fixedTimestamp}.json`);
  });

  it('should generate filename without chiller', () => {
    const topic = 'svBv1.0/ahp/NDATA/TPAD679158';
    const result = getFileName(topic);

    expect(result).toBe(`ahp/TPAD679158/NDATA/${fixedTimestamp}.json`);
  });

  it('should return null for undefined topic', () => {
    const result = getFileName(undefined);
    expect(result).toBeNull();
  });

  it('should return null for topic without enough parts', () => {
    const result = getFileName('something');
    expect(result).toBeNull();
  });
});
