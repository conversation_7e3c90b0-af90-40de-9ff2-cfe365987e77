import { ConfigManifest } from '@carrier/backend-lib-infrastructure';

export class ConfigVariables {
  static readonly adapterName = 'cjc';

  static readonly integrationName = 'cjc';

  static readonly pipelineSdkValidationTurnedOff = 'true';

  static readonly pipelineSdkIsBrickClassValueValidationOff = 'true';

  static readonly primaryStages = ['dev', 'qa', 'preprod', 'prod'];

  static from(manifest: ConfigManifest, partition: string, region: string, account: string, stage: string) {
    const logLevel = manifest.getVariable<string>('logLevel', stage);
    const otelLogServiceAccountRegion = manifest.getVariable<string>('otelLogServiceAccountRegion', stage);
    const otelLogServiceAccountLambdaVersion = manifest.getVariable<number>(
      'otelLogServiceAccountLambdaVersion',
      stage,
    );
    const otelLogServiceAccount = manifest.getVariable<string>('otelLogServiceAccount', stage);
    const lambdaExecWrapper = manifest.getVariable<string>('lambdaExecWrapper', stage);
    const otelLogsSamplingRate = manifest.getVariable<string>('otelLogsSamplingRate', stage);
    const otelBackendExporters = manifest.getVariable<string[]>('otelBackendExporters', stage);
    const otelLogLevel = manifest.getVariable<string[]>('otelLogLevel', stage);
    const otelServiceName = manifest.getVariable<string>('otelServiceName', stage);
    return {
      isEnhancedFanout: false,
      iotRules: [
        {
          name: 'ALL',
          // eslint-disable-next-line no-template-curly-in-string
          kinesisPartitionKey: '${topic(4)}',
          iotRule: `SELECT encode(*, 'base64') as sparkplug, clientId() as clientId, timestamp() as arrivalTime, topic() as topic, traceid() as traceId FROM 'spBv1.0/${this.integrationName}/#'`,
        },
        {
          name: 'LWT',
          // eslint-disable-next-line no-template-curly-in-string
          kinesisPartitionKey: '${topic()}',
          iotRule: `SELECT encode(*, 'base64') as sparkplug, clientId() as clientId, timestamp() as arrivalTime, topic() as topic, traceid() as traceId FROM 'status/${this.integrationName}/connection/#'`,
        },
      ],
      targetStreamArns: {
        data: `arn:${partition}:kinesis:${region}:${account}:stream/carrier-io-main-data-pipeline-${stage}`,
        lifecycle: `arn:${partition}:kinesis:${region}:${account}:stream/carrier-io-main-lifecycle-pipeline-${stage}`,
      },
      s3: {
        bucketName: `cjc-static-files-bucket`,
      },
      dlq: {
        apiIdExportName: `DLQApiGatewayId-${stage}`,
        apiDomainExportName: `DLQApiGatewayDomain-${stage}`,
        numOfAttempts: '5',
      },
      pipelineSdk: {
        isValidationTurnedOff: this.pipelineSdkValidationTurnedOff,
        isBrickClassValueValidationOff: this.pipelineSdkIsBrickClassValueValidationOff,
      },
      logLevel,
      otelBackendExporters,
      otelLogLevel,
      otelServiceName: `${otelServiceName}-${stage}`,
      otelLogServiceAccountRegion,
      otelLogServiceAccountLambdaVersion,
      otelLogServiceAccount,
      lambdaExecWrapper,
      otelLogsSamplingRate,
    };
  }
}
