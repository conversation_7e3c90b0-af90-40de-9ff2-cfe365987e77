/* eslint-disable no-console */
/* eslint-disable no-empty-function */
import { BasicLambdaFunction } from '@carrier/backend-lib-core';
import { PipelinePublisherService, PublishResult, SparkplugMessage } from '@carrier-io/backend-lib-pipeline-sdk';
import { Callback, Context, KinesisStreamEvent } from 'aws-lambda';

import { SparkplugKinesisRecord } from '../models/SparkplugKinesisRecord';
import { LwtRecord } from '../models/LwtRecord';
import { isLwtRecord, logger } from '../util';
import { UpdateMessage } from '../util/updateMessage';
import { handleBrickClasses } from '../util/sparkplug-record';

const pipelineClient = PipelinePublisherService.newWithEnvVar();

export class BaseIntegrationAdapterLambda extends BasicLambdaFunction {
  protected static async onMessage(_sparkplugRecord: SparkplugKinesisRecord | LwtRecord) {}

  protected static async onParseError(_error: unknown, _rawMessage: string) {
    console.log('onParseError:::', _error);
  }

  protected static async onPipelinePublishError(_failedPublishResult: PublishResult) {}

  static override async onColdStart(_event: any, _context: Context, _callback: Callback): Promise<void> {}

  static override async onRequest(event: KinesisStreamEvent, _context: Context): Promise<any> {
    const updateMessage = new UpdateMessage();
    const messagesOrUndefined = await Promise.all(
      event.Records.map(async (record) => {
        try {
          const payload = Buffer.from(record.kinesis.data, 'base64').toString('utf8');
          const parsedPayload = JSON.parse(payload);
          const isLWTRecord = isLwtRecord(parsedPayload.topic, parsedPayload);
          const sparkplugRecord = isLWTRecord
            ? new LwtRecord(record.kinesis.data)
            : new SparkplugKinesisRecord(record.kinesis.data);
          // logger.info('[Gateway Record:::]',JSON.stringify(sparkplugRecord))
          console.log('Gateway Record::: ', JSON.stringify(sparkplugRecord));
          await this.onMessage(sparkplugRecord);
          if (isLWTRecord && !sparkplugRecord.topic) {
            return undefined;
          }
          const messageTypesWithoutEquipmentFamily = ['NBIRTH', 'NDATA', 'NDEATH', 'DDEATH'];

          let assetCategory;

          if (messageTypesWithoutEquipmentFamily.includes(sparkplugRecord.messageType)) {
            assetCategory = 'ahp';
          } else {
            assetCategory = handleBrickClasses(
              sparkplugRecord.sparkplugMessage.metrics,
              sparkplugRecord.topic.split('/')[2],
            );
          }
          return {
            topic: sparkplugRecord.topic,
            payload: sparkplugRecord.sparkplugMessage,
            clientId: sparkplugRecord.clientId,
            arrivalTime: sparkplugRecord.arrivalTime,
            traceId: sparkplugRecord.traceId,
            assetCategory,
          } as SparkplugMessage;
        } catch (err) {
          await this.onParseError(err, record.kinesis.data);
          return undefined;
        }
      }),
    );

    const messages = messagesOrUndefined.filter(Boolean) as SparkplugMessage[];
    if (messages.length) {
      for (let message of messages) {
        try {
          await updateMessage.updatePayload(message);
        } catch (error) {
          logger.error(`[Update Payload ERROR] Error while updating the payload`, { error });
        }
       
        message.topic = message.topic.replace(/(?<=^spBv1\.0\/)[^/]+/, 'ahp');
      }
    }
    // logger.info('Final Messages After Processing _______', JSON.stringify(messages));
    console.log('Final Messages After Processing _______', JSON.stringify(messages));
    const responses = await pipelineClient.publish(messages);
    // logger.info('Responses after publishing messages', JSON.stringify(responses));
    console.log('Responses after publishing messages', JSON.stringify(responses));
    await Promise.allSettled(
      responses.filter(({ result }) => !result).map((response) => this.onPipelinePublishError(response)),
    );
  }
}
