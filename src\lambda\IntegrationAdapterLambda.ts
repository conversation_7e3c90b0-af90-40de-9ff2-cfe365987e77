import { PublishResult, ValidateError } from '@carrier-io/backend-lib-pipeline-sdk';

import { DLQService } from '@carrier-io/backend-lib-dlq-client';
import { SparkplugKinesisRecord } from '../models/SparkplugKinesisRecord';
import { logger, config } from '../util';
import { BaseIntegrationAdapterLambda } from './BaseIntegrationAdapterLambda';

const dlqService = new DLQService({ url: config.dlq.url });

export class IntegrationAdapterLambda extends BaseIntegrationAdapterLambda {
  protected static override readonly logRequest: boolean = false;

  protected static override readonly logResponse: boolean = false;

  protected static override async onMessage(message: SparkplugKinesisRecord) {
    

    logger.info('[ON MESSAGE]', {
      ClientID: message.clientId,
      ArrivalTime: new Date(message.arrivalTime),
      Topic: message.topic,
      Message: message.sparkplugMessage,
      TraceId: message.traceId,
    });
  }

  protected static override async onParseError(error: Error, rawMessage: string) {
    logger.error(`[ON PARSE ERROR] Could not parse record into sparkplug: ${rawMessage}`, { error });
  }

  /**
   * Sends failed Sparkplug messages to DLQ
   * @protected
   * @param responseWithError response from pipeline-sdk library with error and payload failed to send
   */
  protected static override async onPipelinePublishError(
    responseWithError: PublishResult & { error: PublishResult | ValidateError },
  ) {
    const { error, sparkplugMessage } = responseWithError;
    console.log("Errors occured in piblisher pipeline", error)
    console.log("Errors sparkPlug Message", sparkplugMessage)
    const dlqCreateRecordsSuccess = await dlqService.createRecord(sparkplugMessage, error.message);
    if (!dlqCreateRecordsSuccess) {
      logger.error('DLQService', {
        message: 'Could not create dlq records',
        possibleReasons: ['Invalid DLQRecord or dlqService is not available'],
      });
    }
  }
}
