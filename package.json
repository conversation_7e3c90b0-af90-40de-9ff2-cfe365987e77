{"name": "@carrier-io/ahp-integrationadapter-cjc", "version": "0.0.0", "description": "Integration Adapter to process CJC Data", "license": "MIT", "author": "Carrier Digital", "main": "dist/index.js", "dependencies": {"@aws-sdk/client-s3": "^3.830.0", "@aws-sdk/client-secrets-manager": "^3.830.0", "@aws-sdk/client-sns": "^3.421.0", "@aws-sdk/property-provider": "3.272.0", "@aws-sdk/protocol-http": "^3.357.0", "@aws-sdk/s3-request-presigner": "^3.830.0", "@aws-sdk/signature-v4": "^3.357.0", "@carrier-io/backend-lib-dlq-client": "^1.0.2", "@carrier-io/backend-lib-logger": "^1.3.1", "@carrier-io/backend-lib-pipeline-sdk": "^1.11.0", "@carrier-io/backend-lib-registry-service": "^1.6.0", "@carrier-io/pe-lib-otel-logs": "^1.16.2", "@carrier/backend-lib-core": "^3.2.0", "@zephyr/ahp-lib-static-data-config": "^1.9.0", "aws-cdk": "^2.130.0", "aws-sdk": "^2.1692.0", "source-map-support": "^0.5.16", "sparkplug-payload": "1.0.1"}, "devDependencies": {"@carrier-io/backend-lib-infrastructure": "^3.3.5", "@carrier/backend-lib-infrastructure": "^4.11.0", "@tsconfig/node18": "^18.2.2", "@types/aws-lambda": "^8.10.83", "@types/jest": "^29.5.6", "@types/node": "^20.8.7", "@typescript-eslint/eslint-plugin": "^5.38.0", "@typescript-eslint/parser": "^5.38.0", "aws-cdk-lib": "^2.115.0", "cdk": "^2.1020.0", "constructs": "^10.0.0", "esbuild": "0", "eslint": "^8.23.1", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-prettier": "^8.5.0", "eslint-import-resolver-typescript": "^2.7.1", "eslint-plugin-import": "^2.26.0", "eslint-plugin-jest": "^27.4.3", "eslint-plugin-node": "^11.1.0", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-sonarjs": "^0.15.0", "husky": "^8.0.0", "inquirer": "^8.2.0", "jest": "^29.7.0", "jest-sonar-reporter": "^2.0.0", "npm-run-all": "^4.1.5", "path": "^0.12.7", "prettier": "^2.7.1", "sparkplug-payload": "1.0.1", "ts-jest": "^29.1.2", "ts-jest-resolver": "^2.0.1", "ts-node": "^10.9.1", "tsconfig-paths-jest": "^0.0.1", "typedoc": "^0.23.24", "typedoc-bitbucket-theme": "^1.0.0", "typedoc-plugin-markdown": "^3.14.0", "typescript": "^5.3.3"}, "jestSonar": {"reportPath": "coverage", "reportFile": "test-reporter.xml", "indent": 4}, "scripts": {"_lint": "eslint --ext .tsx,.jsx,.ts,.js", "lint": "run-s \"_lint ./src ./infra ./test\"", "ca:login": "./cicd/scripts/rc.sh", "build": "yarn ca:login && npx rimraf ./dist && tsc", "watch": "tsc -w", "local": "sam-beta-cdk local start-api --warm-containers EAGER -n locals.json", "test": "jest --config ./jest.config.js --coverage", "cdk": "cdk", "docs": "rm -rf docs && typedoc", "test:integration": "yarn build && jest  --runInBand --coverage --collectCoverageFrom='./src/**' --config ./jest.config.integration.js --passWithNoTests", "test:unit": "yarn build && jest --coverage --collectCoverageFrom='./src/**' --config ./jest.config.unit.js --passWithNoTests"}, "lint-staged": {"**/*.{json,yml,yaml}": ["prettier --write"], "**/*.{js,ts}": ["eslint --ext .js,.ts --cache --fix"]}}