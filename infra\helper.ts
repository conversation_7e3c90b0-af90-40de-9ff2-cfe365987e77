import { Stack, Stage, Tags } from "aws-cdk-lib";

export class Helper {
  /**
   * Returns true if the stage is production.
   * @param appStage 
   * @returns 
   */
  public static isProduction(appStage: Stage) {
    return ['prod', 'production', 'prod-cn', 'production-cn'].includes(appStage.stageName.toLowerCase());
  }
  /**
   * Tags the stack with the app name and stage.
   * @param stack 
   * @param appName 
   * @param stage 
   */
  public static tagStack(stack: Stack, appName: string, stage: string) {
    Tags.of(stack).add('App', appName);
    Tags.of(stack).add('Namespace', stage);

    const resourceTypesIncluded = ['AWS::Lambda::Function', 'AWS::DynamoDB::Table'];
    Tags.of(stack).add('ValueStreamName', 'Onboarding', {
      includeResourceTypes: resourceTypesIncluded,
    });
    Tags.of(stack).add('AreaPathName', 'AssetModeling', {
      includeResourceTypes: resourceTypesIncluded,
    });
  }
}