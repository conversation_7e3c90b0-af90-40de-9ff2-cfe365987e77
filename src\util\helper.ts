import { Metric } from "./updateMessage";

export const isLwtRecord = (topic: string, payload: any): boolean => {
  const topicParts: string[] = topic.split('/');
  if (topicParts[0].toUpperCase() === 'STATUS' && topicParts[2].toUpperCase() === 'CONNECTION') {
    return true
  }
  return false;
};

export const addCioTagAndUpdateCcnPoints = (metrics, topic:string, cioTag) => {
  const modifiedMetrics = metrics.map((metric: Metric) => {
        if (metric.properties) {
          if (['DBIRTH', 'DDATA'].includes(topic)) {
            metric.properties.cioTags = {
              value: cioTag as string,
              type: 'String',
            };
            if (metric.name && metric.name.includes('/')) {
              metric.name = metric.name.split('/')[1] || metric.name;
           }
          }
        }
        
        return metric;
      });
  return modifiedMetrics;
}

export const getFileName = (topic: string | undefined) => {
  const parts = topic && topic?.indexOf("/") >=0 ? topic.substring(topic.indexOf("/") + 1)?.split("/") : null;
  if (!parts) {
    return null
  }
  const [part, type, gateway, chiller] = parts
  const ifChiller = chiller ? chiller + '/' : ""
  return `${part}/${gateway}/${ifChiller}${type}/${Date.now()}.json`;
}
