import { SparkplugTopicParts, fromString } from './sparkplug-topic';

describe('Sparkplug Topic Parser', () => {
  describe('fromString', () => {
    it('should correctly parse a complete Sparkplug topic string', () => {
      const topicString = 'spBv1.0/Group1/NBIRTH/Edge1/Device1';
      const expected: SparkplugTopicParts = {
        schema: 'spBv1.0',
        groupId: 'Group1',
        messageType: 'NBIRTH',
        edgeNodeId: 'Edge1',
        deviceId: 'Device1'
      };

      const result = fromString(topicString);
      expect(result).toEqual(expected);
    });

    it('should handle topic without deviceId', () => {
      const topicString = 'spBv1.0/Group1/NBIRTH/Edge1';
      const expected: SparkplugTopicParts = {
        schema: 'spBv1.0',
        groupId: 'Group1',
        messageType: 'NBIRTH',
        edgeNodeId: 'Edge1',
        deviceId: ''
      };

      const result = fromString(topicString);
      expect(result).toEqual(expected);
    });

    it('should handle different message types', () => {
      const messageTypes = ['NBIRTH', 'NDEATH', 'DBIRTH', 'DDEATH', 'NDATA', 'DDATA'];
      
      messageTypes.forEach(messageType => {
        const topicString = `spBv1.0/Group1/${messageType}/Edge1/Device1`;
        const result = fromString(topicString);
        
        expect(result.messageType).toBe(messageType);
      });
    });

    it('should handle special characters in topic parts', () => {
      const topicString = 'spBv1.0/Group-1_test/NBIRTH/Edge_123/Device-456';
      const expected: SparkplugTopicParts = {
        schema: 'spBv1.0',
        groupId: 'Group-1_test',
        messageType: 'NBIRTH',
        edgeNodeId: 'Edge_123',
        deviceId: 'Device-456'
      };

      const result = fromString(topicString);
      expect(result).toEqual(expected);
    });

    it('should handle empty string parts correctly', () => {
      const topicString = 'spBv1.0//NBIRTH/Edge1/Device1';
      const expected: SparkplugTopicParts = {
        schema: 'spBv1.0',
        groupId: '',
        messageType: 'NBIRTH',
        edgeNodeId: 'Edge1',
        deviceId: 'Device1'
      };

      const result = fromString(topicString);
      expect(result).toEqual(expected);
    });

    it('should handle topic with extra parts by ignoring them', () => {
      const topicString = 'spBv1.0/Group1/NBIRTH/Edge1/Device1/extra/parts';
      const expected: SparkplugTopicParts = {
        schema: 'spBv1.0',
        groupId: 'Group1',
        messageType: 'NBIRTH',
        edgeNodeId: 'Edge1',
        deviceId: 'Device1'
      };

      const result = fromString(topicString);
      expect(result).toEqual(expected);
    });
  });
});
