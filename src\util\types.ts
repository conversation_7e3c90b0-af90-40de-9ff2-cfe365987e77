import { Long as BaseLong } from 'long';

export interface OOBRecord {
  origin: string;
  payload: any;
}

export type LongValue = Pick<BaseLong, 'low' | 'high' | 'unsigned'>;

export interface MetricProperty {
  value: any;
  type: string;
}

export type PointMetric = {
  name: string;
  type?: string;
  value?: number | string;
  timestamp?: number | LongValue;
  pointId?: string;
  properties?: Record<string, MetricProperty>;
};