import { handleBrickClasses } from './sparkplug-record';
import { PointMetric } from './types';

describe('Sparkplug Record Handler', () => {
  describe('handleBrickClasses', () => {
    let baseMetrics: PointMetric[];

    beforeEach(() => {
      baseMetrics = [
        {
          name: "serialNumber",
          type: "String",
          value: "8901667781",
          properties: {
            brickClass: {
              value: "Serial Number",
              type: "String"
            }
          }
        },
        {
          name: "softwareVersion",
          type: "String",
          value: "FFFFFFFF",
          properties: {
            brickClass: {
              value: "Software Version",
              type: "String"
            }
          }
        },
        {
          name: "picControllerVersion",
          type: "String",
          value: "UC",
          properties: {
            brickClass: {
              value: "PIC Controller Version",
              type: "String"
            }
          }
        },
        {
          name: "equipmentFamily",
          type: "String",
          value: "EDGE3223",
          properties: {
            brickClass: {
              value: "Equipment Family",
              type: "String"
            }
          }
        },
        {
          name: "UNITNO",
          type: "UInt32",
          value: 0,
          properties: {
            brickClass: {
              value: "Chiller_Unit_Number_Status",
              type: "String"
            },
            hasUnit: {
              value: "",
              type: "String"
            }
          }
        },
        {
          name: "ALMCD",
          type: "Int64",
          value:0,
          properties: {
            brickClass: {
              value: "Chiller_Alarm_Code_Status",
              type: "String"
            },
            hasUnit: {
              value: "",
              type: "String"
            }
          }
        }
      ];
    });

    it('should handle DBIRTH message type correctly', () => {
      const result = handleBrickClasses(baseMetrics, 'DBIRTH');
      
      expect(baseMetrics[0].name).toBe('serialNumber');
      expect(baseMetrics[0].properties?.brickClass.value).toBe('Serial_Number');
      expect(result).toBe('EDGE_UCseries');
    });

    it('should verify all metric transformations', () => {
      handleBrickClasses(baseMetrics, 'DBIRTH');

      // Check serialNumber transformation
      expect(baseMetrics[0].name).toBe('serialNumber');
      expect(baseMetrics[0].properties?.brickClass.value).toBe('Serial_Number');
      expect(baseMetrics[0].value).toBe('8901667781');

      // Check softwareVersion transformation
      expect(baseMetrics[1].name).toBe('softwareVersion');
      expect(baseMetrics[1].properties?.brickClass.value).toBe('Software_Version');
      expect(baseMetrics[1].value).toBe('FFFFFFFF');

      // Check picControllerVersion transformation
      expect(baseMetrics[2].name).toBe('picControllerVersion');
      expect(baseMetrics[2].properties?.brickClass.value).toBe('PIC_Controller_Version');
      expect(baseMetrics[2].value).toBe('UC');

      // Check equipmentFamily transformation
      expect(baseMetrics[3].name).toBe('equipmentFamily');
      expect(baseMetrics[3].properties?.brickClass.value).toBe('Equipment_Family');
      expect(baseMetrics[3].value).toBe('EDGE3223');
    });

    it('should handle missing equipmentFamily', () => {
      const metricsWithoutEquipmentFamily = baseMetrics.filter(
        metric => metric.name !== 'equipmentFamily'
      );
      
      const result = handleBrickClasses(metricsWithoutEquipmentFamily, 'DBIRTH');
      expect(result).toBeUndefined();
    });

    it('should handle missing picControllerVersion', () => {
      const metricsWithoutPicController = baseMetrics.filter(
        metric => metric.name !== 'picControllerVersion'
      );
      
      const result = handleBrickClasses(metricsWithoutPicController, 'DBIRTH');
      expect(result).toBeUndefined();
    });
  });
});
