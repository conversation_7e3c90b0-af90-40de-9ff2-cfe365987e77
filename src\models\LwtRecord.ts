/* eslint-disable eqeqeq */
interface Message {
  timestamp: number;
  metrics: any;
}

export class LwtRecord {
  readonly clientId: string;

  readonly arrivalTime: number;

  readonly topic: string;

  readonly sparkplugMessage: Message;

  readonly traceId: string;

  private readonly topicParts: string[];

  get integration(): string {
    return this.topicParts[1];
  }

  get messageType(): string {
    return this.topicParts[2];
  }

  get organizationId(): string {
    return this.topicParts[3];
  }

  constructor(rawMessage: string) {
    console.log("processing LWT record")
    const payload = Buffer.from(rawMessage, 'base64').toString('utf8');
    const record = JSON.parse(payload);
    let topic = '';
    const message: Message = {
      timestamp: new Date().getTime(),
      metrics: [],
    };
    topic = `spBv1.0/${record.topic.split('/')[1]}/NDEATH/${record.topic.split('/')[3]}`;
    this.clientId = record.clientId;
    this.arrivalTime = record.arrivalTime;
    this.topicParts = topic.split('/');
    this.topic = topic;
    this.traceId = record.traceId;
    this.sparkplugMessage = message;
  }
}
