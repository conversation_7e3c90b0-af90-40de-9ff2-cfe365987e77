import { SNSClient, Publish<PERSON>atchCommand, PublishBatchCommandOutput } from '@aws-sdk/client-sns';

export class SNSService {
  private readonly snsTopic = process.env.SNS_TOPIC || '';

  private client = new SNSClient({});

  public async publish(messages: string[]): Promise<PromiseSettledResult<PublishBatchCommandOutput>[]> {
    const promises: Promise<PublishBatchCommandOutput>[] = [];
    const chunkSize = 10;
    for (let i = 0; i < messages.length; i += chunkSize) {
      const chunk = messages.slice(i, i + chunkSize);
      promises.push(
        this.client.send(
          new PublishBatchCommand({
            PublishBatchRequestEntries: chunk.map((m, index) => ({
              Id: `${i}-${index}`,
              Message: m,
            })),
            TopicArn: this.snsTopic,
          }),
        ),
      );
    }
    return Promise.allSettled(promises);
  }
}
