import { SecretsManagerClient, GetSecretValueCommand, GetSecretValueResponse } from '@aws-sdk/client-secrets-manager';

export class SecretManager {
  private readonly client: SecretsManagerClient = new SecretsManagerClient({
    region: process.env.REGION,
  });

  async getSecretValue(secretId: string): Promise<GetSecretValueResponse> {
    const input = {
      SecretId: secretId,
    };
    const command = new GetSecretValueCommand(input);
    return this.client.send(command);
  }
}
