import { normalizeQUDTUnitNotation } from "@zephyr/ahp-lib-static-data-config";
import { addCioTagAndUpdateCcnPoints } from "./helper";

// Define the Metric interface
export interface Metric {
  name: string;
  type: string;
  value: any;
  properties?: {
    [key: string]: {
      value: string;
      type: string;
    };
  };
  parameters?: [];
}

// Define the Payload interface
export interface Payload {
  timestamp: number | string;
  metrics: Metric[];
  seq: number | string;
  topic: string | any;
  payload: any;
}

export interface Paramater {
  name: string;
  type: string;
  value: string;
}

// Define the UpdateMessage class
class UpdateMessage {
  // Method to update the payload
  async updatePayload(message: any): Promise<any> {
    // console.log('MESSAGE :::', JSON.stringify(message));
    // Extract the cioTag from the "General/cioTags" metric
    let cioTag: string | undefined = undefined;
    const topic = message.topic.split('/')[2];
    const updatedMetrics = message.payload.metrics.filter((metric: Metric) => {
      if (['General/cioTags', 'cioTags'].includes(metric.name)) {
        cioTag = metric.value;
        return false;
      }
      return true;
    });

    if (topic?.includes('DBIRTH')) {
      const brickSchemaMetric = message.payload.metrics.find((m: Metric) => m.name === 'brickSchema');
      if (brickSchemaMetric?.value?.parameters) {
        // Find the brickClass parameter
        const brickClassParam = brickSchemaMetric.value.parameters.find(
          (param: { name: string; }) => param.name === 'brickClass'
        );
        
        // Add partName parameter using brickClass value ( this is for AMS)
        brickSchemaMetric.value.parameters.push({
          name: 'partName',
          type: 'String',
          value: brickClassParam?.value ?? ''
        });
      }
    }

    if (['DBIRTH', 'DDATA'].includes(topic)) {
      // If cioTag is undefined, throw an error
      if (!cioTag) {
        throw new Error('cioTag not found in the payload.');
      }
    }

    // Add cioTag to each metric's properties
    const updatedMetricsWithCioTag = addCioTagAndUpdateCcnPoints(updatedMetrics, topic, cioTag)
    // Convert metrics recived as metrics to imperial before publishing.
    const imperialConvertedMetrics = normalizeQUDTUnitNotation(updatedMetricsWithCioTag);
    message.payload.metrics = imperialConvertedMetrics;
    //replace cjc with ahp in topic
    if (message.topic) {
      message.topic = message.topic.replace(/(?<=^spBv1\.0\/)[^/]+/, 'ahp');
    }
    return message;
  }
}

export { UpdateMessage };
