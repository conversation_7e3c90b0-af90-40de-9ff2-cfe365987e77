import path from 'path';

import { ConfigManifest, EnvironmentUtils, Manifest } from '@carrier/backend-lib-infrastructure';
import { App } from 'aws-cdk-lib';

export class ManifestHelper {
  private readonly stage;

  private readonly accountId: string;

  private readonly region: string;

  private readonly isDevMode: boolean;

  private readonly manifest: Manifest;

  constructor(app: App, stage: string, accountId: string, region: string) {
    this.stage = stage;
    this.accountId = accountId;
    this.region = region;
    this.isDevMode = app.node.tryGetContext('devMode');
    // Fetch the manifest details if available in cdk.json | cdk.context.json file
    this.manifest = app.node.tryGetContext('manifest');
  }

  public getManifest(): any {
    let manifestData = this.manifest;
    // If manifest is missing, extract information from `manifest.yml` file
    if (!manifestData) {
      const filePath = path.resolve(__dirname, '../..', 'manifest.yml');
      manifestData = EnvironmentUtils.getEnvManifest(this.accountId, this.stage, filePath, this.isDevMode);
    }

    const manifest = new ConfigManifest(manifestData);
    // Replace all the keys in manifest to the developer stage.
    if (this.isDevMode) {
      // Based on deployment region replace `dev-cn` other wise `dev` with stage
      const replaceKey = `dev`;
      manifest.updateManifestKeys(replaceKey, this.stage);
    }

    if (!manifestData || !JSON.stringify(manifestData).includes(this.stage)) {
      console.log(`
      Trying to deploy in developer stage|namespace?
      use 'yarn cdk deploy -c stage=yourstage -c devMode=true *'`);
      process.exit(128);
    }
    return manifest;
  }
}
