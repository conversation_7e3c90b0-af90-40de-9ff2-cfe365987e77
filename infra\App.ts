import { App, Tags, Stage } from 'aws-cdk-lib';
import { IntegrationAdapterStack } from './stacks/IntegrationAdapterStack';
import { Config } from './config/Config';
import { ManifestHelper } from './manifest/ManifestHelper';
import { CJCIntegrationPayloadS3Stack } from './stacks/CJCIntegrationPayloadS3Stack';

const app = new App();

const awsAccountId = process.env.CDK_DEFAULT_ACCOUNT as string;
const awsRegion = process.env.CDK_DEFAULT_REGION as string;
const stage = app.node.tryGetContext('stage') ?? "dev";
const env = { env: { account: awsAccountId, region: awsRegion, stage } };

const manifestHelper = new ManifestHelper(app, stage, awsAccountId, awsRegion);

const manifest = manifestHelper.getManifest();

const AppStage = new Stage(app, stage, {
  env: {
    account: awsAccountId,
    region: awsRegion,
  },
});
// eslint-disable-next-line no-new
const integrationAdapterStack = new IntegrationAdapterStack(
  AppStage,
  manifest,
  `Carrier-IO-${Config.adapterName}-Integration-Adapter`,
  env,
);

new CJCIntegrationPayloadS3Stack(
  AppStage,
  manifest,
  `Carrier-io-${Config.adapterName}-Integration-Adapter-Logs-To-S3`,
  integrationAdapterStack.adapterStream,
  integrationAdapterStack.streamConsumer,
  env
)

Tags.of(integrationAdapterStack).add('App', Config.adapterName);
Tags.of(integrationAdapterStack).add('Namespace', stage);
