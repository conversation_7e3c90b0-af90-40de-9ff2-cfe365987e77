---
pipeline:
  template: aws-cdk-node@1
parameters:
  - &default-parameters
    aws_partition: aws
    aws_account_id: '************'
    aws_region_name: us-east-1
    jira_environment_name: dev
    jira_environment_type: development
    node_version: '20.12.0'
    cdk_contexts:
      stage: dev
      manifest:
        manifestVersion: '1.0'
        variables:
          - type: host
            items:
              - key: dev
                value: dev.carrier.io
          - type: ahpAccounts
            items:
              - key: dev
                value: '************'
          - type: regionCN
            items:
              - key: regions
                value: 'cn-north-1,cn-northwest-1'
          - type: logLevel
            items:
              - key: dev
                value: 'INFO'
          - type: otelBackendExporters
            items:
              - key: dev
                value:
                  - 'newrelic'
          - type: otelLogLevel
            items:
              - key: dev
                value:
                  - 'info'
                  - 'error'
                  - 'warn'
          - type: otelServiceName
            items:
              - key: dev
                value: 'carrier-io-backend-integration-adapter-cjc'
          - type: otelLogServiceAccount
            items:
              - key: dev
                value: "************"
          - type: otelLogServiceAccountLambdaVersion
            items:
              - key: dev
                value: 9
          - type: otelLogServiceAccountRegion
            items:
              - key: dev
                value: us-east-1
          - type: lambdaExecWrapper
            items:
              - key: dev
                value: /opt/otel-initializer
          - type: otelLogsSamplingRate
            items:
              - key: dev
                value: "75"